using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.ComponentModel;

namespace Shinsoft.DDI.Bll
{
    /// <summary>
    /// 产品业务逻辑层
    /// 提供产品相关的业务操作，包括增删改查、数据验证等功能
    /// </summary>
    [Description("产品业务逻辑层")]
    public class ProductBll : BaseBll
    {
        #region Constructs

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUser">操作用户</param>
        public ProductBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUniqueName">操作用户唯一名称</param>
        public ProductBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="operatorUser">操作用户</param>
        public ProductBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="bll">业务逻辑层接口</param>
        public ProductBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region Product 产品管理

        /// <summary>
        /// 添加产品
        /// </summary>
        /// <param name="entity">产品实体</param>
        /// <returns>操作结果</returns>
        public BizResult<Product> AddProduct(Product entity)
        {
            var result = new BizResult<Product>();

            // 验证必填字段
            if (entity.NameCn.IsEmpty())
            {
                result.Error("请输入产品中文名称");
            }

            if (entity.CommonName.IsEmpty())
            {
                result.Error("请输入通用名称");
            }

            if (entity.ManufacturerId == Guid.Empty)
            {
                result.Error("请选择生产厂家");
            }

            if (result.Success)
            {
                // 检查中文名称是否重复
                var existByNameCn = this.GetEntity<Product>(p => p.NameCn == entity.NameCn);
                if (existByNameCn != null)
                {
                    result.Error("该产品名称已存在");
                }
            }

            if (result.Success)
            {
                // 验证药企是否存在
                var manufacturer = this.Get<Manufacturer>(entity.ManufacturerId);
                if (manufacturer == null)
                {
                    result.Error("指定的药企不存在");
                }
            }

            if (result.Success)
            {
                entity.ID = CombGuid.NewGuid();
                entity.Code = GetSerialNumber(ConstDefinition.CodePrefix.Product_CodePrefix);
                entity.EnumStatus = ProductStatus.Valid;
                entity = this.Add(entity);
                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 更新产品
        /// </summary>
        /// <param name="entity">产品实体</param>
        /// <returns>操作结果</returns>
        public BizResult<Product> UpdateProduct(Product entity)
        {
            var result = new BizResult<Product>();

            var dbEntity = this.Get<Product>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("产品不存在");
            }
            else
            {
                // 验证必填字段
                if (entity.NameCn.IsEmpty())
                {
                    result.Error("请输入产品中文名称");
                }

                if (entity.CommonName.IsEmpty())
                {
                    result.Error("请输入通用名称");
                }

                if (entity.ManufacturerId == Guid.Empty)
                {
                    result.Error("请选择生产厂家");
                }

                if (result.Success)
                {
                    // 检查中文名称是否重复
                    var existByNameCn = this.GetEntity<Product>(p => p.NameCn == entity.NameCn && p.ID != entity.ID);
                    if (existByNameCn != null)
                    {
                        result.Error("该产品名称已存在");
                    }
                }

                if (result.Success)
                {
                    // 验证药企是否存在
                    var manufacturer = this.Get<Manufacturer>(entity.ManufacturerId);
                    if (manufacturer == null)
                    {
                        result.Error("指定的药企不存在");
                    }
                }
                if (result.Success)
                {
                    entity = this.Update(entity);
                    result.Data = entity;
                }
            }

            return result;
        }

        /// <summary>
        /// 删除产品
        /// </summary>
        /// <param name="id">产品ID</param>
        /// <returns>操作结果</returns>
        public BizResult DeleteProduct(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<Product>(id);
            if (entity == null)
            {
                result.Error("产品不存在");
            }
            else
            {
                // 检查是否有关联的产品规格
                var hasProductSpecs = this.GetEntity<ProductSpec>(p => p.ProductId == id);
                if (hasProductSpecs != null)
                {
                    result.Error("该产品下存在产品规格，无法删除");
                }
                else
                {
                    this.Delete(entity);
                }
            }

            return result;
        }

        public BizResult StopProduct(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<Product>(id);
            if (entity == null)
            {
                result.Error("产品不存在");
            }
            else
            {
                //TODO 处理业务逻辑

                entity.EnumStatus = ProductStatus.Stoped;
                this.Update(entity);
            }

            return result;
        }

        public BizResult EnableProduct(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<Product>(id);
            if (entity == null)
            {
                result.Error("产品不存在");
            }
            else
            {
                //TODO 处理业务逻辑

                entity.EnumStatus = ProductStatus.Valid;
                this.Update(entity);
            }

            return result;
        }


        #endregion Product 产品管理
    }
}