import axios from '@/utils/axios'

/**
 * 货主相关API
 */
export const shipperApi = {
  /**
   * 查询货主列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.shipperName 货主名称
   * @param {string} params.shipperCode 货主编码
   * @param {string} params.order 排序
   * @returns {Promise} API响应
   */
  queryShipper(params) {
    return axios.get('/Shipper/QueryShipper', { params })
  },

  /**
   * 获取货主详情
   * @param {string} id 货主ID
   * @returns {Promise} API响应
   */
  getShipper(id) {
    return axios.get('/Shipper/Get', { params: { id } })
  },

  /**
   * 新增货主
   * @param {Object} data 货主数据
   * @param {string} data.code 货主编码
   * @param {string} data.name 货主名称
   * @param {string} data.shortName 简称
   * @param {number} data.enumStatus 状态
   * @param {string} data.address 地址
   * @param {string} data.contactPerson 联系人
   * @param {string} data.telephone 电话
   * @param {string} data.eMail 邮箱
   * @param {boolean} data.isDefault 是否默认货主
   * @returns {Promise} API响应
   */
  addShipper(data) {
    return axios.post('/Shipper/Add', data)
  },

  /**
   * 编辑货主
   * @param {Object} data 货主数据
   * @param {string} data.id 货主ID
   * @param {string} data.code 货主编码
   * @param {string} data.name 货主名称
   * @param {string} data.shortName 简称
   * @param {number} data.enumStatus 状态
   * @param {string} data.address 地址
   * @param {string} data.contactPerson 联系人
   * @param {string} data.telephone 电话
   * @param {string} data.eMail 邮箱
   * @param {boolean} data.isDefault 是否默认货主
   * @returns {Promise} API响应
   */
  editShipper(data) {
    return axios.post('/Shipper/Edit', data)
  },

  /**
   * 删除货主
   * @data {Object} data 货主数据
   * @returns {Promise} API响应
   */
  deleteShipper(data) {
    return axios.post('/Shipper/Delete', data)
  },

    /**
   * 停用货主
   * @data {Object} data 货主数据
   * @returns {Promise} API响应
   */
  stopShipper(data) {
    return axios.post('/Shipper/StopShipper', data)
  },

    /**
   * 启用货主
   * @data {Object} data 货主数据
   * @returns {Promise} API响应
   */
  enableShipper(data) {
    return axios.post('/Shipper/EnableShipper', data)
  },
  
  /**
   * 导出货主列表
   * @param {Object} params 导出参数
   * @param {string} params.shipperName 货主名称
   * @param {string} params.shipperCode 货主编码
   * @returns {Promise} API响应，返回文件流
   */
  exportShipper(params) {
    return axios.post('/Shipper/ExportShipper', params, {
      responseType: 'arraybuffer'
    })
  }
}

export default shipperApi