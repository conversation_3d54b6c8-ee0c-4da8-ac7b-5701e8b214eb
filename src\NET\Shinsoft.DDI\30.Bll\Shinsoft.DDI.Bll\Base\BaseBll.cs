﻿using Microsoft.EntityFrameworkCore;
using Shinsoft.DDI.Dal;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Bll
{
    public abstract class BaseBll : Repository<BizDbContext>, IRepo
    {
        #region Constructs

        protected BaseBll(IUser? operatorUser = null)
            : base(operatorUser)
        {
        }

        protected BaseBll(string operatorUniqueName)
            : base(operatorUniqueName)
        {
        }

        protected BaseBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser)
        {
        }

        protected BaseBll(IRepo bll)
            : base(bll.SysRepo)
        {
        }

        #endregion Constructs

        #region IRepo

        protected virtual IRepo SysRepo => this.SysBll;
        protected virtual IRepo BizRepo => this.CompanyBll;
        protected virtual IRepo FileRepo => this.FileBll;
        protected virtual IRepo LogRepo => this.LogBll;
        protected virtual IRepo MailRepo => this.MailBll;

        protected virtual SysBll SysBll => this.GetRepo<SysBll>();
        protected virtual CompanyBll CompanyBll => this.GetRepo<CompanyBll>();
        protected virtual FileBll FileBll => this.GetRepo<FileBll>();
        protected virtual LogBll LogBll => this.GetRepo<LogBll>();
        protected virtual MailBll MailBll => this.GetRepo<MailBll>();

        IRepo IRepo.SysRepo => this.SysRepo;
        IRepo IRepo.BizRepo => this.BizRepo;
        IRepo IRepo.FileRepo => this.FileRepo;
        IRepo IRepo.LogRepo => this.LogRepo;
        IRepo IRepo.MailRepo => this.MailRepo;

        #endregion IRepo

        #region Operator User

        /// <summary>
        /// 操作用户（可能为空）
        /// </summary>
        public new User? OperatorUser => base.OperatorUser as User;

        /// <summary>
        /// 操作用户ID（可能为空）
        /// </summary>
        public virtual Guid? OperatorUserId => this.OperatorUser?.ID;

        /// <summary>
        /// 操作员工（可能为空）
        /// </summary>

        public virtual Employee? OperatorEmployee => this.OperatorUser?.Employee;

        /// <summary>
        /// 操作员工ID（可能为空）
        /// </summary>
        public virtual Guid? OperatorEmployeeId => this.OperatorEmployee?.ID;

        public virtual string? OperatorCulture => this.OperatorUser?.Culture;

        #endregion Operator User

        #region Current User

        /// <summary>
        /// 当前用户（操作用户为空时报错）
        /// </summary>
        public new User CurrentUser => (User)base.CurrentUser;

        /// <summary>
        /// 当前用户ID（操作用户为空时报错）
        /// </summary>
        public virtual Guid CurrentUserId => this.CurrentUser.ID;

        /// <summary>
        /// 当前员工（为空时报错）
        /// </summary>
        public virtual Employee CurrentEmployee => this.CurrentUser.Employee ?? throw new InvalidOperationException("当前员工不存在");

        /// <summary>
        /// 当前员工ID（为空时报错）
        /// </summary>
        public virtual Guid CurrentEmployeeId => this.CurrentEmployee.ID;

        public virtual string CurrentCulture => this.CurrentUser.Culture;

        #endregion Current User

        #region Current Shipper

        /// <summary>
        /// 当前或者
        /// </summary>
        public virtual Shipper CurrentShipper => this.CurrentUser.CurrentShipper;

        /// <summary>
        /// 当前货主ID
        /// </summary>
        public virtual Guid CurrentShipperId => this.CurrentShipper.ID;

        #endregion Current Shipper

        #region SysCache

        private SysCache? _sysCache = null;

        protected virtual SysCache SysCache => _sysCache ??= this.GetRequiredService<SysCache>();

        protected virtual SysCache GetSysCache()
        {
            var cache = this.GetRequiredService<SysCache>();

            if (this.ServiceScope != null)
            {
                cache.SetServiceScope(this.ServiceScope);
            }

            return cache;
        }

        public override void ClearServiceScope()
        {
            base.ClearServiceScope();

            _sysCache?.ClearServiceScope();
        }

        #endregion SysCache

        #region CompanyCache

        protected virtual CompanyCachePool CompanyCachePool => this.GetRequiredService<CompanyCachePool>();

        protected virtual CompanyCache GetCompanyCache(Guid? companyId)
        {
            if (!companyId.HasValue)
            {
                throw new InvalidOperationException($"【{this.GetType().Name}】:无法获取当前公司ID");
            }

            return this.CompanyCachePool.GetCompanyCache(companyId.Value);
        }

        #endregion CompanyCache

        #region GetRepo

        protected override TRepo GetRepo<TRepo>()
        {
            return this.GetRepo<TRepo>(Config.DefaultCompanyId);
        }

        #endregion GetRepo

        protected override DbContextOptionsBuilder BuildDbOptions()
        {
            var optionsBuilder = new DbContextOptionsBuilder();

            optionsBuilder.UseDbConfig(nameof(BizDbContext), Config.DecryptConnStr);

            return optionsBuilder;
        }

        private string ExecSerialNumber(string prefix, int? count = null, DateTime? date = null, string? dateFormat = null, int? seedLength = null)
        {
            count ??= 1;
            date ??= SysDateTime.Now;

            var dbParams = new List<DbParameter>
            {
                this.CreateParameter("CompanyId", this.CurrentEmployee.CompanyId),
                this.CreateParameter("Prefix", prefix),
                this.CreateParameter("Date", date),
                this.CreateParameter("Count", count),
                this.CreateParameter("OperatorUser", this.OperatorUniqueName)
            };

            if (!dateFormat.IsEmpty())
            {
                dbParams.Add(this.CreateParameter("DateFormat", dateFormat));
            }

            if (seedLength > 0)
            {
                dbParams.Add(this.CreateParameter("SeedLength", seedLength));
            }

            var snParam = this.CreateParameter("SerialNumber", DbType.String);

            dbParams.Add(snParam);

            this.ExecuteNonQuery(CommandType.StoredProcedure, "dbo.sp_GetSerialNumber", dbParams);

            var value = snParam.Value.AsString();

            return value;
        }

        public virtual string GetSerialNumber(string prefix, DateTime? date = null, string? dateFormat = null, int? seedLength = null)
        {
            var sn = this.ExecSerialNumber(prefix, 1, date, dateFormat, seedLength);

            return sn;
        }

        public virtual List<string> GetSerialNumbers(string prefix, int count, DateTime? date = null, string? dateFormat = null, int? seedLength = null)
        {
            var sn = this.ExecSerialNumber(prefix, count, date, dateFormat, seedLength);

            var sns = sn.Split(';', options: StringSplitOptions.TrimEntries).ToList();

            return sns;
        }
    }
}