/**
 * 将Portal项目的静态路由转换为SQL脚本
 * 用于插入到VueRoute和VueRouteMeta表中
 */

import { appRouter } from './www/src/router/router.js';
import { v4 as uuidv4 } from 'uuid';

// 示例公司ID，实际使用时需要替换为真实的公司ID
const COMPANY_ID = '00000000-0000-0000-0000-000000000000';

/**
 * 生成UUID（模拟NEWSEQUENTIALID()）
 */
function generateUUID() {
  return uuidv4().toUpperCase();
}

/**
 * 转义SQL字符串
 */
function escapeSqlString(str) {
  if (!str) return "''";
  return "'" + str.replace(/'/g, "''") + "'";
}

/**
 * 生成路由的SQL插入语句
 */
function generateRouteSql(route, parentId = null, rank = 0) {
  const routeId = generateUUID();
  const sqlStatements = [];
  
  // 处理路径 - 确保以/开头
  let path = route.path;
  if (parentId && !path.startsWith('/')) {
    path = '/' + path;
  }
  
  // 生成VueRoute插入语句
  const routeSql = `
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    '${routeId}',
    '${COMPANY_ID}',
    ${parentId ? `'${parentId}'` : 'NULL'},
    ${escapeSqlString(route.name)},
    ${escapeSqlString(path)},
    ${escapeSqlString(route.redirect || '')},
    1, -- Valid
    1, -- IsSys (系统路由)
    0, -- Deleted
    'SYSTEM', -- Creator
    GETDATE() -- CreateTime
);`;

  sqlStatements.push(routeSql);
  
  // 生成VueRouteMeta插入语句
  const title = route.title || route.meta?.title || route.name;
  const icon = route.icon || 'compose'; // 默认图标
  const showLink = route.meta?.showLink !== false; // 默认显示
  const anonymous = route.meta?.anonymous || false;
  const requireAuth = route.meta?.requireAuth || false;
  
  // 根据requireAuth生成权限配置
  let auths = null;
  if (requireAuth && !anonymous) {
    // 基于路由名称生成权限代码
    const authCode = generateAuthCode(route.name, route.path);
    auths = `["${authCode}"]`;
  }
  
  const metaSql = `
INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    '${routeId}',
    '${COMPANY_ID}',
    ${escapeSqlString(title)},
    ${escapeSqlString(icon)},
    ${rank}, -- Rank
    ${showLink ? 1 : 0}, -- ShowLink
    ${route.alwaysShow ? 1 : 'NULL'}, -- AlwaysShow
    ${anonymous ? 1 : 'NULL'}, -- Anonymous
    1, -- IsBreadcrumbLink (默认true)
    ${auths ? escapeSqlString(auths) : 'NULL'} -- Auths
);`;

  sqlStatements.push(metaSql);
  
  // 处理子路由
  if (route.children && route.children.length > 0) {
    route.children.forEach((child, index) => {
      const childSql = generateRouteSql(child, routeId, (rank * 100) + index + 1);
      sqlStatements.push(...childSql);
    });
  }
  
  return sqlStatements;
}

/**
 * 根据路由名称和路径生成权限代码
 */
function generateAuthCode(name, path) {
  // 权限代码映射表
  const authMapping = {
    // 首页
    'index': 'Home:View',
    
    // 主数据管理
    'shipperList': 'Master:Shipper:Query',
    'distributorList': 'Master:Distributor:Query', 
    'productList': 'Master:Product:Query',
    
    // DDI配置监控
    'ddiConfig': 'DDI:Config:View',
    'distributorConfig': 'DDI:Config:Distributor',
    'productConfig': 'DDI:Config:Product',
    'clientConfig': 'DDI:Config:Client',
    'ddiMonitor': 'DDI:Monitor:View',
    
    // 数据清洗
    'productAlias': 'DataCleaning:ProductAlias:Query',
    'distributorAlias': 'DataCleaning:DistributorAlias:Query',
    'importSalesflow': 'DataCleaning:Import:Query',
    
    // 数据查询
    'dailyQuery': 'SalesFlow:Daily:View',
    'dailySalesFlowQuery': 'SalesFlow:Daily:SalesFlow:Query',
    'dailyPurchaseQuery': 'SalesFlow:Daily:Purchase:Query',
    'dailyInventoryQuery': 'SalesFlow:Daily:Inventory:Query',
    'monthlyQuery': 'SalesFlow:Monthly:View',
    'monthlySalesFlowQuery': 'SalesFlow:Monthly:SalesFlow:Query',
    'monthlyPurchaseQuery': 'SalesFlow:Monthly:Purchase:Query',
    
    // 系统管理
    'userlist': 'System:Employee:Query',
    'dictionarylist': 'System:Dictionary:Query',
    'logManagement': 'System:Log:View',
    'operationlog': 'System:Log:Operation:Query',
    'exceptionlog': 'System:Log:Exception:Query',
    'clientlog': 'System:Log:Client:Query'
  };
  
  return authMapping[name] || `${name}:Query`;
}

/**
 * 生成完整的SQL脚本
 */
function generateFullSql() {
  const sqlStatements = [];

  // 添加脚本头部注释
  sqlStatements.push(`
-- =============================================
-- Portal项目路由数据插入脚本
-- 生成时间: ${new Date().toISOString()}
-- 说明: 将Portal项目的静态路由转换为VueRoute和VueRouteMeta表数据
-- 注意: 请将COMPANY_ID替换为实际的公司ID
-- =============================================

-- 设置公司ID变量（请替换为实际的公司ID）
DECLARE @CompanyId UNIQUEIDENTIFIER = '${COMPANY_ID}';

-- 开始事务
BEGIN TRANSACTION;

TRY
`);

  // 生成每个顶级路由的SQL
  appRouter.forEach((route, index) => {
    const routeSql = generateRouteSql(route, null, index + 1);
    sqlStatements.push(...routeSql);
  });

  // 添加脚本尾部
  sqlStatements.push(`
    -- 提交事务
    COMMIT TRANSACTION;
    PRINT 'Portal路由数据插入成功！';

END TRY
BEGIN CATCH
    -- 回滚事务
    ROLLBACK TRANSACTION;

    -- 输出错误信息
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();

    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH;
`);

  return sqlStatements.join('\n');
}

// 生成SQL脚本
const sqlScript = generateFullSql();

// 输出到控制台
console.log(sqlScript);

// 如果在Node.js环境中，可以写入文件
if (typeof require !== 'undefined') {
  const fs = require('fs');
  const path = require('path');

  const outputPath = path.join(__dirname, 'portal-routes-insert.sql');
  fs.writeFileSync(outputPath, sqlScript, 'utf8');
  console.log(`\nSQL脚本已生成到: ${outputPath}`);
}
