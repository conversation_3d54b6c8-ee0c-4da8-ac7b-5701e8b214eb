﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public struct ApiIdentityKey : IIdentityKey
    {
        /// <summary>
        /// 货主
        /// </summary>
        public Guid ShipperId { get; set; }

        #region IIdentityKey

        public string Culture { get; set; }

        public string UserId { get; set; }

        public string? EmployeeId { get; set; }

        public string? AgentId { get; set; }

        public string? RoleId { get; set; }

        public int IdentitySeed { get; set; }

        #endregion IIdentityKey
    }
}