﻿CREATE TABLE [dbo].[ReceiverClient]
(
    [ID]                        UNIQUEIDENTIFIER            NOT NULL    CONSTRAINT [DF_ReceiverClient_ID]  DEFAULT (NEWSEQUENTIALID()),
    [ShareReceiverClientId]     UNIQUEIDENTIFIER            NULL,
    [VerifyFrequency]           INT                         NOT NULL,
    [AutoUpdate]                BIT                         NOT NULL,
    [Version]                   NVARCHAR(50)                NULL,
    [UpdateToVersion]           NVARCHAR(50)                NOT NULL,
    [UpdateUrl]                 NVARCHAR(500)               NOT NULL,
    [ScheduleType]              NVARCHAR(50)                NOT NULL,
    [ScheduleDay]               NVARCHAR(50)                NOT NULL,
    [ScheduleTime]              NVARCHAR(50)                NOT NULL,
    [SourceType]                NVARCHAR(50)                NOT NULL,
    [TargetType]                NVARCHAR(50)                NOT NULL,
    [LogType]                   NVARCHAR(50)                NOT NULL,
    [DbConnectType]             NVARCHAR(50)                NOT NULL,
    [DBConnect]                 NVARCHAR(500)               NOT NULL,
    [DBSqlB]                    NVARCHAR(500)               NOT NULL,
    [DBSqlS]                    NVARCHAR(500)               NOT NULL,
    [DBSqlI]                    NVARCHAR(500)               NOT NULL,
    [FixPathB]                  NVARCHAR(500)               NOT NULL,
    [FixPathS]                  NVARCHAR(500)               NOT NULL,
    [FixPathI]                  NVARCHAR(500)               NOT NULL,
    [FixFileB]                  NVARCHAR(500)               NOT NULL,
    [FixFileS]                  NVARCHAR(500)               NOT NULL,
    [FixFileI]                  NVARCHAR(500)               NOT NULL,
    [WSUrl]                     NVARCHAR(500)               NULL,
    [WSUsername]                NVARCHAR(500)               NULL,
    [WSPassword]                NVARCHAR(500)               NULL,
    [HTTPUrl]                   NVARCHAR(500)               NULL,
    [HTTPUsername]              NVARCHAR(500)               NULL,
    [HTTPPassword]              NVARCHAR(500)               NULL,
    [FTPServer]                 NVARCHAR(500)               NULL,
    [FTPPort]                   NVARCHAR(500)               NULL,
    [FtpType]                   NVARCHAR(50)                NULL,
    [FTPUsername]               NVARCHAR(500)               NULL,
    [FTPPassword]               NVARCHAR(500)               NULL,
    [FTPPath]                   NVARCHAR(500)               NULL,
    [SavePath]                  NVARCHAR(500)               NULL,
    [LogHttpUrl]                NVARCHAR(500)               NULL,
    [LogHttpUsername]           NVARCHAR(500)               NULL,
    [LogHttpPassword]           NVARCHAR(500)               NULL,
    [HTTPProxy]                 NVARCHAR(500)               NULL,
    [HTTPProxyUsername]         NVARCHAR(500)               NULL,
    [HTTPProxyPassword]         NVARCHAR(500)               NULL,
    [FTPProxy]                  NVARCHAR(500)               NULL,
    [FTPProxyUsername]          NVARCHAR(500)               NULL,
    [FTPProxyPassword]          NVARCHAR(500)               NULL,
    [DeleteClientData]          NVARCHAR(500)               NULL,
    [FileEncoding]              NVARCHAR(50)                NULL,
    [EnvironmentVariable]       NVARCHAR(500)               NULL,
    [Mode]                      NVARCHAR(50)                NULL,
    [RestartTime]               NVARCHAR(500)               NULL,
    [OtherXml]                  NVARCHAR(MAX)               NOT NULL,
    [Status]                    NVARCHAR(50)                NULL,
    [Frequency]                 INT                         NULL,
    [FileType]                  NVARCHAR(50)                NULL,
    [CollectionMethod]          NVARCHAR(50)                NULL,
    CONSTRAINT [PK_ReceiverClient] PRIMARY KEY CLUSTERED ([ID]),
    CONSTRAINT [FK_ReceiverClient_Receiver_00_Receiver_Client] FOREIGN KEY ([ID]) REFERENCES [dbo].[Receiver] ([ID]),
    CONSTRAINT [FK_ReceiverClient_ReceiverClient_00_ShareReceiverClient] FOREIGN KEY ([ShareReceiverClientId]) REFERENCES [dbo].[ReceiverClient] ([ID]),
)
GO

EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'收货方配置',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClient',
    @level2type = NULL,
    @level2name = NULL
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'当前版本',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClient',
    @level2type = N'COLUMN',
    @level2name = N'Version'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'状态',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClient',
    @level2type = N'COLUMN',
    @level2name = N'Status'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'服务检测频率',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClient',
    @level2type = N'COLUMN',
    @level2name = N'Frequency'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'文件类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClient',
    @level2type = N'COLUMN',
    @level2name = N'FileType'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'采集方式',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'ReceiverClient',
    @level2type = N'COLUMN',
    @level2name = N'CollectionMethod'
GO