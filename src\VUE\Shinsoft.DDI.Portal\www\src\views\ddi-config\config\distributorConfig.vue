<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>DDI配置与监控</el-breadcrumb-item>
        <el-breadcrumb-item>DDI配置</el-breadcrumb-item>
        <el-breadcrumb-item>经销商配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="8" type="flex">
        <el-col :span="4">
          <el-input v-model="filter.code" placeholder="编号" clearable />
        </el-col>
        <el-col :span="4">
          <el-input v-model="filter.name" placeholder="名称" clearable />
        </el-col>
        <el-col :span="4">
          <el-select v-model="filter.status" placeholder="状态" clearable class="custom-select">
            <el-option label="正常" value="正常" />
            <el-option label="锁定" value="锁定" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-input v-model="filter.authCode" placeholder="授权码" clearable />
        </el-col>
        <el-col :span="4">
          <el-select v-model="filter.targetType" placeholder="采集方式" clearable class="custom-select">
            <el-option label="客户端" value="客户端" />
            <el-option label="FTP" value="FTP" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="openNewDistributorConfig">新增经销商配置</el-button>
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="distributorList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="distributorCode" label="编号" min-width="90" />
        <el-table-column prop="distributorName" label="经销商名称" min-width="220" />
        <el-table-column prop="provinceName" label="省份" width="100" />
        <el-table-column prop="cityName" label="城市" width="100" />
        <el-table-column prop="lastCollectTime" label="最新采集时间" width="160" />
        <el-table-column prop="targetType" label="采集方式" width="120" />
        <el-table-column prop="updateToVersion" label="版本" width="180" />
        <el-table-column prop="enumStatusDesc" label="状态" width="80">
          <template #default="{ row }">
            {{ row.status || '待接入' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="90" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="配置" placement="top">
              <el-button icon="Setting" circle size="small" @click="openDistributorDetail(row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button icon="Delete" circle size="small" @click="removeDistributor(row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="filter.pageIndex" v-model:page-size="filter.pageSize"
        :page-sizes="pageSizeOpts" :total="totalCount" layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize" @current-change="changePage" />
    </div>

    <!-- 经销商详细配置弹窗 -->
    <DistributorConfigDialog 
      v-model="showDetailDialog" 
      :dialog-title="dialogTitle" 
      :detail-form="detailForm"
      :save-loading="saveDetailLoading" 
      :current-distributor-id="currentDistributor?.id" 
      @save="handleSaveDetail"
      @close="handleCloseDetailDialog" 
      @edit-mapping="handleEditMapping" 
      @detail-loaded="handleDetailLoaded" 
    />
  </div>
</template>

<script>
import DistributorConfigDialog from './components/distributorConfigDialog.vue'
import { configApi } from '@/api/configApi'
import { Plus } from '@element-plus/icons-vue'

export default {
  name: 'DistributorConfig',
  components: {
    DistributorConfigDialog,
    Plus
  },

  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      // 详细配置弹窗相关
      showDetailDialog: false,
      saveDetailLoading: false,

      dialogTitle: '',
      currentDistributor: null,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        code: '',
        name: '',
        targetType: '',
        order: 'ID desc'
      },
      totalCount: 0,
      distributorList: [],
      // 详细配置表单数据
      detailForm: {
        code: '',
        name: '',
        // 基本配置字段
        autoUpdate: true,
        dbConnect: '',
        dbConnectType: '',
        dbSqlB: '',
        dbSqlI: '',
        dbSqlS: '',
        deleteClientData: '',
        distributorAdress: '',
        distributorCode: '',
        distributorName: '',
        environmentVariable: '',
        fileEncoding: '',
        fixFileB: '',
        fixFileI: '',
        fixFileS: '',
        fixPathB: '',
        fixPathI: '',
        fixPathS: '',
        frequency: null,
        ftpPassword: '',
        ftpPath: '',
        ftpPort: '',
        ftpProxy: '',
        ftpProxyPassword: '',
        ftpProxyUsername: '',
        ftpServer: '',
        ftpType: '',
        ftpUsername: '',
        httpPassword: '',
        httpProxy: '',
        httpProxyPassword: '',
        httpProxyUsername: '',
        httpUrl: '',
        httpUsername: '',
        id: '',
        logHttpPassword: '',
        logHttpUrl: '',
        logHttpUsername: '',
        logType: '',
        mode: '',
        otherXml: '',
        restartTime: '',
        savePath: '',
        scheduleDay: '',
        scheduleTime: '',
        scheduleType: '',
        sourceType: '',
        status: '',
        targetType: '',
        updateToVersion: '',
        updateUrl: '',
        verifyFrequency: '',
        version: '',
        wsPassword: '',
        wsUrl: '',
        wsUsername: '',

        // 规则配置字段 - 销售
        salesPreRuleCheck: false,
        salesFileValidation: false,
        salesFileValidationExecution: false,
        salesFileValidationSplicing: false,
        salesCleanRuleCheck: false,
        salesStoreNameValidation: false,
        salesProductValidation: false,
        salesQuantityValidation: false,
        salesTerminalNameValidation: false,

        // 规则配置字段 - 库存
        inventoryPreRuleCheck: false,
        inventoryFileValidation: false,
        inventoryFileValidationExecution: false,
        inventoryFileValidationSplicing: false,
        inventoryCleanRuleCheck: false,
        inventoryStoreNameValidation: false,
        inventoryProductValidation: false,
        inventoryQuantityValidation: false,
        inventoryTerminalNameValidation: false,

        // 规则配置字段 - 购进
        purchasePreRuleCheck: false,
        purchaseFileValidation: false,
        purchaseFileValidationExecution: false,
        purchaseFileValidationSplicing: false,
        purchaseCleanRuleCheck: false,
        purchaseStoreNameValidation: false,
        purchaseProductValidation: false,
        purchaseQuantityValidation: false,
        purchaseTerminalNameValidation: false,

        // 列映射
        ColumnMappings: []
      }
    }
  },
  mounted() {
    this.loadDistributorList();
  },
  methods: {
    // 查询方法
    search() {
      this.filter.pageIndex = 1;
      this.loadDistributorList();
    },

    // 加载经销商列表数据
    async loadDistributorList() {
      this.loading = true;

      try {
        // 构建查询参数
        const params = {
          pageIndex: this.filter.pageIndex,
          pageSize: this.filter.pageSize,
          order: this.filter.order
        };

        // 添加过滤条件
        if (this.filter.code) {
          params.code = this.filter.code;
        }
        if (this.filter.name) {
          params.name = this.filter.name;
        }
        if (this.filter.targetType) {
          params.targetType = this.filter.targetType;
        }

        // 调用后端API
        const response = await configApi.queryReceiverClient(params);

        if (response.data && response.data.success !== false) {
          this.distributorList = response.data.datas || [];
          this.totalCount = response.data.total || 0;
        } else {
          this.$message.error(response.data.messages?.[0] || '查询失败');
          this.distributorList = [];
          this.totalCount = 0;
        }
      } catch (error) {
        console.error('查询经销商配置失败:', error);
        this.$message.error('查询失败，请稍后重试');
        this.distributorList = [];
        this.totalCount = 0;
      } finally {
        this.loading = false;
      }
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.pageSize = size;
      this.filter.pageIndex = 1;
      this.loadDistributorList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.pageIndex = page;
      this.loadDistributorList();
    },

    // 删除经销商
    async removeDistributor(row) {
      try {
        await this.$confirm(`确定要删除经销商"${row.distributorName}"配置吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        console.log(row);
        // 调用删除API
        const response = await configApi.deleteReceiverClient(row.id);

        if (response.data && response.data.success !== false) {
          this.$message.success('删除成功');
          // 重新加载列表
          this.loadDistributorList();
        } else {
          this.$message.error(response.data.messages?.[0] || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除经销商配置失败:', error);
          this.$message.error('删除失败，请稍后重试');
        }
      }
    },

    // 新增经销商配置
    openNewDistributorConfig() {
      // 重置表单数据
      this.resetDetailForm();

      // 设置为新增模式
      this.currentDistributor = null;
      this.dialogTitle = '新增经销商配置';

      // 显示对话框
      this.showDetailDialog = true;
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    },

    // 打开经销商详细配置
    openDistributorDetail(row) {
      this.currentDistributor = row;
      this.dialogTitle = `${row.distributorCode}-${row.distributorName}`;

      // 先重置表单并显示基本信息
      this.resetDetailForm();
      this.detailForm.code = row.distributorCode;
      this.detailForm.name = row.distributorName;

      // 显示对话框，详细数据将由对话框组件自动加载
      this.showDetailDialog = true;
    },

    // 处理详细数据加载完成
    handleDetailLoaded(detailData) {
      // 更新表单数据
      this.detailForm = {
        // 基本信息
        ownerName: detailData.ownerName || '科盟贸易', // 货主名称
        code: detailData.distributorCode,
        name: detailData.distributorName,
        distributorAdress: detailData.distributorAdress,

        // 基本配置字段
        autoUpdate: detailData.autoUpdate,
        dbConnect: detailData.dbConnect,
        dbConnectType: detailData.dbConnectType,
        dbSqlB: detailData.dbSqlB,
        dbSqlI: detailData.dbSqlI,
        dbSqlS: detailData.dbSqlS,
        deleteClientData: detailData.deleteClientData,
        distributorAdress: detailData.distributorAdress,
        distributorCode: detailData.distributorCode,
        distributorName: detailData.distributorName,
        environmentVariable: detailData.environmentVariable,
        fileEncoding: detailData.fileEncoding,
        fixFileB: detailData.fixFileB,
        fixFileI: detailData.fixFileI,
        fixFileS: detailData.fixFileS,
        fixPathB: detailData.fixPathB,
        fixPathI: detailData.fixPathI,
        fixPathS: detailData.fixPathS,
        frequency: detailData.frequency,
        ftpPassword: detailData.ftpPassword,
        ftpPath: detailData.ftpPath,
        ftpPort: detailData.ftpPort,
        ftpProxy: detailData.ftpProxy,
        ftpProxyPassword: detailData.ftpProxyPassword,
        ftpProxyUsername: detailData.ftpProxyUsername,
        ftpServer: detailData.ftpServer,
        ftpType: detailData.ftpType,
        ftpUsername: detailData.ftpUsername,
        httpPassword: detailData.httpPassword,
        httpProxy: detailData.httpProxy,
        httpProxyPassword: detailData.httpProxyPassword,
        httpProxyUsername: detailData.httpProxyUsername,
        httpUrl: detailData.httpUrl,
        httpUsername: detailData.httpUsername,
        id: detailData.id,
        logHttpPassword: detailData.logHttpPassword,
        logHttpUrl: detailData.logHttpUrl,
        logHttpUsername: detailData.logHttpUsername,
        logType: detailData.logType,
        mode: detailData.mode || 'Normal',
        otherXml: detailData.otherXml,
        //restartTime: detailData.restartTime,
        savePath: detailData.savePath,
        scheduleDay: detailData.scheduleDay,
        scheduleTime: detailData.scheduleTime,
        scheduleType: detailData.scheduleType,
        sourceType: detailData.sourceType,
        status: detailData.status || '待接入',
        targetType: detailData.targetType,
        updateToVersion: detailData.updateToVersion,
        updateUrl: detailData.updateUrl,
        verifyFrequency: detailData.verifyFrequency,
        version: detailData.version,
        wsPassword: detailData.wsPassword,
        wsUrl: detailData.wsUrl,
        wsUsername: detailData.wsUsername,
        frequency: detailData.frequency,

        // 规则配置字段
        salesPreRuleCheck: detailData.salesPreRuleCheck || false,
        salesFileValidation: detailData.salesFileValidation || false,
        salesFileValidationExecution: detailData.salesFileValidationExecution || false,
        salesFileValidationSplicing: detailData.salesFileValidationSplicing || false,
        salesCleanRuleCheck: detailData.salesCleanRuleCheck || false,
        salesStoreNameValidation: detailData.salesStoreNameValidation || false,
        salesProductValidation: detailData.salesProductValidation || false,
        salesQuantityValidation: detailData.salesQuantityValidation || false,
        salesTerminalNameValidation: detailData.salesTerminalNameValidation || false,

        // 库存规则配置
        inventoryPreRuleCheck: detailData.inventoryPreRuleCheck || false,
        inventoryFileValidation: detailData.inventoryFileValidation || false,
        inventoryFileValidationExecution: detailData.inventoryFileValidationExecution || false,
        inventoryFileValidationSplicing: detailData.inventoryFileValidationSplicing || false,
        inventoryCleanRuleCheck: detailData.inventoryCleanRuleCheck || false,
        inventoryStoreNameValidation: detailData.inventoryStoreNameValidation || false,
        inventoryProductValidation: detailData.inventoryProductValidation || false,
        inventoryQuantityValidation: detailData.inventoryQuantityValidation || false,
        inventoryTerminalNameValidation: detailData.inventoryTerminalNameValidation || false,

        // 购进规则配置
        purchasePreRuleCheck: detailData.purchasePreRuleCheck || false,
        purchaseFileValidation: detailData.purchaseFileValidation || false,
        purchaseFileValidationExecution: detailData.purchaseFileValidationExecution || false,
        purchaseFileValidationSplicing: detailData.purchaseFileValidationSplicing || false,
        purchaseCleanRuleCheck: detailData.purchaseCleanRuleCheck || false,
        purchaseStoreNameValidation: detailData.purchaseStoreNameValidation || false,
        purchaseProductValidation: detailData.purchaseProductValidation || false,
        purchaseQuantityValidation: detailData.purchaseQuantityValidation || false,
        purchaseTerminalNameValidation: detailData.purchaseTerminalNameValidation || false
      };
    },

    // 重置详细表单
    resetDetailForm() {
      this.detailForm = {
        // 基本配置字段
        ownerName: '科盟贸易', // 货主名称
        code: '',
        name: '',
        fullName: '',
        address: '',
        customerCode1: '',
        customerCode2: '',
        customerCode3: '',
        level: '',
        batch: '',
        ruleStatus: '科盟贸易',
        region: '',
        status: '正常',
        collectMethod: '',
        fileFormat: 'CSV',
        fileDirectory: '',
        authCode: '',
        distributorContact: '',
        keyuanContact: '',

        // 客户端配置字段
        runMode: 'auto',
        frequency: 60,
        currentVersion: '',
        autoUpgrade: 'false',
        targetVersion: '',
        upgradeDownloadUrl: '',
        repeatMode: 'daily',
        runTime: new Date(2023, 0, 1, 23, 0),
        restartTime: new Date(2023, 0, 1, 3, 0),
        dataSourceType: 'database',
        dbConnectionType: 'sqlserver',
        dbConnectionString: '',
        dbBuyInSql: '',
        dbSellOutSql: '',
        dbInventorySql: '',
        uploadMethod: 'http',
        httpUploadUrl: '',
        httpUploadUsername: '',
        httpUploadPassword: '',
        logRecordMethod: 'webservice',
        logWebServiceUrl: '',
        logWebServiceUsername: '',
        logWebServicePassword: '',
        otherParameters: '',

        // 规则配置字段
        salesPreRuleCheck: false,
        salesFileValidation: false,
        salesFileValidationExecution: false,
        salesFileValidationSplicing: false,
        salesCleanRuleCheck: false,
        salesStoreNameValidation: false,
        salesProductValidation: false,
        salesQuantityValidation: false,
        salesTerminalNameValidation: false,

        // 库存规则配置
        inventoryPreRuleCheck: false,
        inventoryFileValidation: false,
        inventoryFileValidationExecution: false,
        inventoryFileValidationSplicing: false,
        inventoryCleanRuleCheck: false,
        inventoryStoreNameValidation: false,
        inventoryProductValidation: false,
        inventoryQuantityValidation: false,
        inventoryTerminalNameValidation: false,

        // 购进规则配置
        purchasePreRuleCheck: false,
        purchaseFileValidation: false,
        purchaseFileValidationExecution: false,
        purchaseFileValidationSplicing: false,
        purchaseCleanRuleCheck: false,
        purchaseStoreNameValidation: false,
        purchaseProductValidation: false,
        purchaseQuantityValidation: false,
        purchaseTerminalNameValidation: false
      };
    },

    // 关闭详细配置弹窗
    handleCloseDetailDialog() {
      this.showDetailDialog = false;
      this.currentDistributor = null;
      this.resetDetailForm();
    },

    // 重置详细配置表单
    resetDetailForm() {
      this.detailForm = {
        // 基本配置字段
        ownerName: '科盟贸易', // 货主名称
        // 基本配置字段
        autoUpdate: true,
        dbConnect: '',
        dbConnectType: '',
        dbSqlB: '',
        dbSqlI: '',
        dbSqlS: '',
        deleteClientData: '',
        distributorAdress: '',
        distributorCode: '',
        distributorName: '',
        environmentVariable: '',
        fileEncoding: '',
        fixFileB: '',
        fixFileI: '',
        fixFileS: '',
        fixPathB: '',
        fixPathI: '',
        fixPathS: '',
        frequency: null,
        ftpPassword: '',
        ftpPath: '',
        ftpPort: '',
        ftpProxy: '',
        ftpProxyPassword: '',
        ftpProxyUsername: '',
        ftpServer: '',
        ftpType: '',
        ftpUsername: '',
        httpPassword: '',
        httpProxy: '',
        httpProxyPassword: '',
        httpProxyUsername: '',
        httpUrl: '',
        httpUsername: '',
        id: '',
        logHttpPassword: '',
        logHttpUrl: '',
        logHttpUsername: '',
        logType: '',
        mode: '',
        otherXml: '',
        restartTime: '',
        savePath: '',
        scheduleDay: '',
        scheduleTime: '',
        scheduleType: '',
        sourceType: '',
        status: '',
        targetType: '',
        updateToVersion: '',
        updateUrl: '',
        verifyFrequency: '',
        version: '',
        wsPassword: '',
        wsUrl: '',
        wsUsername: '',

        frequency: 60,

        // 规则配置字段 - 销售
        salesPreRuleCheck: false,
        salesFileValidation: false,
        salesFileValidationExecution: false,
        salesFileValidationSplicing: false,
        salesCleanRuleCheck: false,
        salesStoreNameValidation: false,
        salesProductValidation: false,
        salesQuantityValidation: false,
        salesTerminalNameValidation: false,

        // 规则配置字段 - 库存
        inventoryPreRuleCheck: false,
        inventoryFileValidation: false,
        inventoryFileValidationExecution: false,
        inventoryFileValidationSplicing: false,
        inventoryCleanRuleCheck: false,
        inventoryStoreNameValidation: false,
        inventoryProductValidation: false,
        inventoryQuantityValidation: false,
        inventoryTerminalNameValidation: false,

        // 规则配置字段 - 购进
        purchasePreRuleCheck: false,
        purchaseFileValidation: false,
        purchaseFileValidationExecution: false,
        purchaseFileValidationSplicing: false,
        purchaseCleanRuleCheck: false,
        purchaseStoreNameValidation: false,
        purchaseProductValidation: false,
        purchaseQuantityValidation: false,
        purchaseTerminalNameValidation: false
      };
    },

    // 保存详细配置
    async handleSaveDetail(saveData) {
      this.saveDetailLoading = true;

      try {
        let response;
        // 判断是新增还是编辑
        if (this.currentDistributor?.id) {
          // 编辑模式
          response = await configApi.updateReceiverClient(saveData);
        } else {
          // 新增模式
          if (!saveData.id) {
            this.$message.error('请先选择经销商');
            return;
          }
          response = await configApi.addReceiverClient(saveData);
        }

        if (response.data && response.data.success !== false) {
          this.$message.success(this.currentDistributor?.id ? '配置更新成功' : '配置创建成功');
          this.handleCloseDetailDialog();
          // 重新加载列表
          this.loadDistributorList();
        } else {
          this.$message.error(response.data.messages?.[0] || '保存失败');
        }
      } catch (error) {
        console.error('保存经销商配置失败:', error);
        this.$message.error('保存失败，请稍后重试');
      } finally {
        this.saveDetailLoading = false;
      }
    },

    // 编辑列映射
    editMapping(row) {
      this.$message.info(`编辑映射：${row.fieldName}`);
      // TODO: 实现编辑映射功能
    },

    // 下载文件
    downloadFile(fileName) {
      this.$message.info(`下载文件：${fileName}`);
      // TODO: 实现文件下载功能
    },

    // 处理编辑列映射
    handleEditMapping(row, index) {
      // 这里可以添加编辑列映射的逻辑
      console.log('编辑列映射:', row, index);
      this.$message.info('编辑列映射功能待实现');
    },

    // 解析时间字符串为Date对象
    parseTime(timeStr) {
      if (!timeStr) return new Date(2023, 0, 1, 0, 0);

      try {
        // 假设时间格式为 "HH:mm" 或 "HH:mm:ss"
        const parts = timeStr.split(':');
        const hour = parseInt(parts[0]) || 0;
        const minute = parseInt(parts[1]) || 0;
        return new Date(2023, 0, 1, hour, minute);
      } catch (error) {
        console.warn('解析时间失败:', timeStr, error);
        return new Date(2023, 0, 1, 0, 0);
      }
    },

    // 格式化Date对象为时间字符串
    formatTime(dateObj) {
      if (!dateObj) return '';

      try {
        const hour = dateObj.getHours().toString().padStart(2, '0');
        const minute = dateObj.getMinutes().toString().padStart(2, '0');
        return `${hour}:${minute}`;
      } catch (error) {
        console.warn('格式化时间失败:', dateObj, error);
        return '';
      }
    }

  }
}
</script>
