using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.ComponentModel;

namespace Shinsoft.DDI.Bll
{
    /// <summary>
    /// 产品规格业务逻辑层
    /// 提供产品规格相关的业务操作，包括增删改查、数据验证等功能
    /// </summary>
    [Description("产品规格业务逻辑层")]
    public class ProductSpecBll : BaseBll
    {
        #region Constructs

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUser">操作用户</param>
        public ProductSpecBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUniqueName">操作用户唯一名称</param>
        public ProductSpecBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="operatorUser">操作用户</param>
        public ProductSpecBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="bll">业务逻辑层接口</param>
        public ProductSpecBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region ProductSpec 产品规格管理

        /// <summary>
        /// 添加产品规格
        /// </summary>
        /// <param name="entity">产品规格实体</param>
        /// <returns>操作结果</returns>
        public BizResult<ProductSpec> AddProductSpec(ProductSpec entity)
        {
            var result = new BizResult<ProductSpec>();

            // 验证必填字段
            if (entity.ProductId == Guid.Empty)
            {
                result.Error("请选择产品");
            }


            if (entity.Spec.IsEmpty())
            {
                result.Error("请输入规格");
            }

            if (result.Success)
            {
                // 验证产品是否存在
                var product = this.Get<Product>(entity.ProductId);
                if (product == null)
                {
                    result.Error("指定的产品不存在");
                }
            }

            if (result.Success)
            {
                // 检查规格是否重复
                var existBySpec = this.GetEntity<ProductSpec>(p => p.Spec == entity.Spec && p.ProductId == entity.ProductId && p.PharmaceuticalFactory == entity.PharmaceuticalFactory &&
                                                                        p.MaterialGroup == entity.MaterialGroup);
                if (existBySpec != null)
                {
                    result.Error("该产品下已存在相同的规格");
                }
            }

            if (result.Success)
            {
                // 验证剂型是否存在（如果提供了剂型ID）
                if (entity.DosageFormId.HasValue)
                {
                    var dosageForm = this.Get<Dict>(entity.DosageFormId.Value);
                    if (dosageForm == null)
                    {
                        result.Error("指定的剂型不存在");
                    }
                }
            }

            if (result.Success)
            {
                entity.Code = GetSerialNumber(ConstDefinition.CodePrefix.ProductSpec_CodePrefix);
                entity.EnumStatus = ProductSpecStatus.Valid;
                entity = this.Add(entity);
                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 更新产品规格
        /// </summary>
        /// <param name="entity">产品规格实体</param>
        /// <returns>操作结果</returns>
        public BizResult<ProductSpec> UpdateProductSpec(ProductSpec entity)
        {
            var result = new BizResult<ProductSpec>();

            var dbEntity = this.Get<ProductSpec>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("产品规格不存在");
            }
            else
            {
                // 验证必填字段
                if (entity.ProductId == Guid.Empty)
                {
                    result.Error("请选择产品");
                }

                if (entity.Code.IsEmpty())
                {
                    result.Error("请输入规格编码");
                }

                if (entity.Spec.IsEmpty())
                {
                    result.Error("请输入规格");
                }

                if (result.Success)
                {
                    // 验证产品是否存在
                    var product = this.Get<Product>(entity.ProductId);
                    if (product == null)
                    {
                        result.Error("指定的产品不存在");
                    }
                }

                if (result.Success)
                {
                    // 检查规格编码是否重复
                    var existByCode = this.GetEntity<ProductSpec>(p => p.Code == entity.Code && p.ID != entity.ID);
                    if (existByCode != null)
                    {
                        result.Error("该产品下已存在相同编码的规格");
                    }
                }

                if (result.Success)
                {
                    // 检查规格是否重复
                    var existBySpec = this.GetEntity<ProductSpec>(p => p.Spec == entity.Spec && 
                                                                        p.ProductId == entity.ProductId && 
                                                                        p.PharmaceuticalFactory == entity.PharmaceuticalFactory &&
                                                                        p.MaterialGroup == entity.MaterialGroup &&
                                                                        p.ID != entity.ID);
                    if (existBySpec != null)
                    {
                        result.Error("该产品下已存在相同的规格");
                    }
                }

                if (result.Success)
                {
                    // 验证剂型是否存在（如果提供了剂型ID）
                    if (entity.DosageFormId.HasValue)
                    {
                        var dosageForm = this.Get<Dict>(entity.DosageFormId.Value);
                        if (dosageForm == null)
                        {
                            result.Error("指定的剂型不存在");
                        }
                    }
                }

                if (result.Success)
                {
                    entity = this.Update(entity);
                    result.Data = entity;
                }
            }

            return result;
        }

        /// <summary>
        /// 删除产品规格
        /// </summary>
        /// <param name="id">产品规格ID</param>
        /// <returns>操作结果</returns>
        public BizResult DeleteProductSpec(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<ProductSpec>(id);
            if (entity == null)
            {
                result.Error("产品规格不存在");
            }
            else
            {
                // 检查是否有关联的货主产品配置
                var hasShipperProductSpecs = this.GetEntity<ShipperProductSpec>(s => s.ProductSpecId == id);
                if (hasShipperProductSpecs != null)
                {
                    result.Error("该产品规格下存在货主产品配置，无法删除");
                }
                else
                {
                    this.Delete(entity);
                }
            }

            return result;
        }

        public BizResult StopProductSpec(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<ProductSpec>(id);
            if (entity == null)
            {
                result.Error("产品规格不存在");
            }
            else
            {
                // TODO 处理业务逻辑

                entity.EnumStatus = ProductSpecStatus.Stoped;
                this.Update(entity);
            }

            return result;
        }

        public BizResult EnableProductSpec(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<ProductSpec>(id);
            if (entity == null)
            {
                result.Error("产品规格不存在");
            }
            else
            {
                // TODO 处理业务逻辑

                entity.EnumStatus = ProductSpecStatus.Valid;
                this.Update(entity);
            }

            return result;
        }

        #endregion ProductSpec 产品规格管理
    }
}