﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shinsoft.Core.Caching;
using Shinsoft.DDI.Api.Providers;

namespace Shinsoft.DDI.Api
{
    [Authorize]
    [ApiController]
    [Route("[controller]/[action]")]
    [ApiExplorerSettings(GroupName = "Main")]
    public abstract class BaseApiController<TRepo> : BaseController<TRepo, User, IdentityUser, Identity>, ICompanyController
        where TRepo : class, IRepo
    {
        protected BaseApiController(TRepo? repo = null)
            : base(repo)
        {
            if (repo != null && repo is ICompanyRepo companyRepo && this.OperatorCompanyId == null)
            {
                companyRepo.CurrentCompanyId = this.ComputedCompanyId;
            }
        }

        protected virtual new UserProvider UserProvider => this.GetRequiredService<IUserProvider, UserProvider>();

        protected virtual SysBll SysBll => this.GetRepo<SysBll>();
        protected virtual CompanyBll CompanyBll => this.GetRepo<CompanyBll>();
        protected virtual FileBll FileBll => this.GetRepo<FileBll>();
        protected virtual MailBll MailBll => this.GetRepo<MailBll>();
        protected virtual LogBll LogBll => this.GetRepo<LogBll>();

        #region Operator User

        public virtual Guid? OperatorUserId => this.OperatorUser?.ID;

        /// <summary>
        /// 操作员工（可能为空）
        /// </summary>
        protected virtual Employee? OperatorEmployee => this.OperatorUser?.Employee;

        /// <summary>
        /// 操作员工ID（可能为空）
        /// </summary>
        protected virtual Guid? OperatorEmployeeId => this.OperatorEmployee?.ID;

        #endregion Operator User

        #region Current User

        /// <summary>
        /// 当前用户ID（操作员工为空时报错）
        /// </summary>
        public virtual Guid CurrentUserId => this.CurrentUser.ID;

        /// <summary>
        /// 当前员工（为空时报错）
        /// </summary>
        public virtual Employee CurrentEmployee => this.CurrentUser.Employee ?? throw new InvalidOperationException("当前员工不存在");

        /// <summary>
        /// 当前员工ID（为空时报错）
        /// </summary>
        public virtual Guid CurrentEmployeeId => this.CurrentEmployee.ID;

        #endregion Current User

        #region Current Shipper

        /// <summary>
        /// 当前用户ID（操作员工为空时报错）
        /// </summary>
        public virtual Guid CurrentShipperId => this.CurrentUser.CurrentShipperId;

        public virtual Shipper CurrentShipper => this.CurrentUser.CurrentShipper;

        #endregion Current Shipper

        #region Company

        protected virtual Guid? OperatorCompanyId => this.OperatorUser?.OperatorCompanyId;

        protected virtual Guid CurrentCompanyId => this.CurrentEmployee.CompanyId;

        protected virtual Guid ComputedCompanyId => this.OperatorCompanyId ?? Config.DefaultCompanyId;

        #endregion Company

        #region ICompanyController

        Guid? ICompanyController.OperatorCompanyId => this.OperatorCompanyId;

        string? ICompanyController.OperatorCompanyCode => this.OperatorCompanyId.HasValue
                ? this.SysCache.GetCompany(this.OperatorCompanyId.Value)?.Code
                : null;

        string? ICompanyController.OperatorCompanyName => this.OperatorCompanyId.HasValue
                ? this.SysCache.GetCompany(this.OperatorCompanyId.Value)?.Name
                : null;

        #endregion ICompanyController

        #region SysCache

        protected virtual SysCache SysCache => this.GetRequiredService<SysCache>();

        #endregion SysCache

        #region CompanyCache

        protected virtual CompanyCachePool CompanyCachePool => this.GetRequiredService<CompanyCachePool>();

        protected CompanyCache GetCompanyCache(Guid companyId)
        {
            return this.CompanyCachePool.GetCompanyCache(companyId);
        }

        private CompanyCache? _companyCache;

        protected virtual CompanyCache CompanyCache
        {
            get
            {
                if (_companyCache == null)
                {
                    var companyId = this.OperatorCompanyId;

                    if (!companyId.HasValue)
                    {
                        //     companyId = Config.DefaultCompanyId;

                        throw new InvalidOperationException("当前用户为空或当前公司ID为空，因此无法获取公司缓存");
                    }

                    _companyCache = this.GetCompanyCache(companyId.Value);
                }
                return _companyCache;
            }
        }

        #endregion CompanyCache

        #region GetRepo

        protected override TRepository GetRepo<TRepository>()
        {
            return this.GetRepo<TRepository, Guid>(this.ComputedCompanyId);
        }

        protected virtual TRepository GetRepo<TRepository>(Guid companyId)
            where TRepository : IRepo
        {
            return this.GetRepo<TRepository, Guid>(companyId);
        }

        #endregion GetRepo
    }
}