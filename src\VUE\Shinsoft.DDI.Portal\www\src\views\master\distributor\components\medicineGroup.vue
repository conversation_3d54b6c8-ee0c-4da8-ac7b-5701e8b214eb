<template>
  <!-- 上级单位选择弹框 -->
  <el-dialog v-model="dialogVisible" title="选择上级单位" width="900px" append-to-body>
    <div class="medicine-group-selector">
      <!-- 搜索框 -->
      <div class="search-bar" style="margin-bottom: 16px;">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-cascader v-model="selectedCity" :options="cityList" placeholder="省份/城市"
              clearable @change="handleCascaderChange" :props="{
                expandTrigger: 'hover',
                checkStrictly: true,
                emitPath: true
              }" />
          </el-col>
          <el-col :span="6">
            <el-input v-model="searchForm.name" placeholder="请输入名称" clearable>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input v-model="searchForm.code" placeholder="请输入编码" clearable>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-button icon="Search" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 表格 -->
      <el-table :data="dataList" height="350" @row-click="handleRowClick" highlight-current-row
        style="cursor: pointer;">
        <el-table-column prop="name" label="收货方名称" width="200" />
        <el-table-column prop="code" label="Code" width="150" />
        <el-table-column prop="provinceName" label="省份" width="100" />
        <el-table-column prop="cityName" label="城市" width="100" />
        <el-table-column prop="receiverTypeName" label="收货方类型">
          <template #default="{ row }">
            {{ row.receiverTypeLevelOneName }}-{{ row.receiverTypeLevelTwoName }}-{{ row.receiverTypeLevelThreeName }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ row }">
            <el-button circle size="small" @click.stop="handleSelectItem(row)">
              <el-icon>
                <CircleCheck />
              </el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
        <el-pagination v-model:current-page="pagination.pageIndex"
          v-model:page-size="pagination.pageSize" :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange" @current-change="handlePageChange" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { CircleCheck } from '@element-plus/icons-vue'
import { receiverApi } from '@/api/receiverApi'
import { selectorApi } from '@/api/selectorApi'

/**
 * 上级单位选择器组件
 * 用于选择上级单位的弹框组件
 */
export default {
  name: 'MedicineGroup',
  components: {
    CircleCheck
  },
  props: {
    // 对话框显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 排除的记录ID（编辑模式下排除当前记录）
    excludeId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'select'],
  setup(props, { emit }) {
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 数据列表
    const dataList = ref([])
    // 级联选择器选中的省市
    const selectedCity = ref([])
    // 省市级联选择器数据
    const cityList = ref([])
    
    // 搜索表单数据
    const searchForm = reactive({
      provinceId: '',          // 省份ID
      cityId: '',              // 城市ID
      name: '',         // 名称搜索关键词
      code: ''          // 编码搜索关键词
    })
    
    // 分页数据
    const pagination = reactive({
      pageIndex: 1,
      pageSize: 10,
      total: 0
    })

    /**
     * 加载省市级联选项
     */
    const loadCityOptions = async () => {
      try {
        const response = await selectorApi.ProvinceCitySelect()
        if (response.data) {
          cityList.value = response.data || []
        }
      } catch (error) {
        console.error('加载省市选项失败:', error)
      }
    }

    /**
     * 加载数据列表
     */
    const loadDataList = async () => {
      try {
        const params = {
          pageIndex: pagination.pageIndex,
          pageSize: pagination.pageSize
        }

        // 添加搜索条件
        if (searchForm.provinceId) {
          params.provinceId = searchForm.provinceId
        }
        if (searchForm.cityId) {
          params.cityId = searchForm.cityId
        }
        if (searchForm.name) {
          params.name = searchForm.name
        }
        if (searchForm.code) {
          params.code = searchForm.code
        }

        // 排除指定记录
        if (props.excludeId) {
          params.excludeId = props.excludeId
        }
        
        // 只查询启用状态的记录
        params.status = [10]
        
        const response = await receiverApi.queryReceiver(params)
        if (response.data && response.data.success) {
          dataList.value = response.data.datas || []
          pagination.total = response.data.total || 0
        }
      } catch (error) {
        console.error('加载数据列表失败:', error)
      }
    }

    /**
     * 级联选择器变化处理
     */
    const handleCascaderChange = (value) => {
      if (value && value.length > 0) {
        searchForm.provinceId = value[0] || ''
        searchForm.cityId = value[1] || ''
      } else {
        searchForm.provinceId = ''
        searchForm.cityId = ''
      }
    }

    /**
     * 搜索处理
     */
    const handleSearch = () => {
      pagination.pageIndex = 1
      loadDataList()
    }

    /**
     * 分页大小改变处理
     */
    const handlePageSizeChange = (size) => {
      pagination.pageSize = size
      pagination.pageIndex = 1
      loadDataList()
    }

    /**
     * 页码改变处理
     */
    const handlePageChange = (page) => {
      pagination.pageIndex = page
      loadDataList()
    }

    /**
     * 表格行点击处理
     */
    const handleRowClick = (row) => {
      handleSelectItem(row)
    }

    /**
     * 选择项目处理
     */
    const handleSelectItem = (row) => {
      emit('select', row)
      handleClose()
    }

    /**
     * 关闭弹框处理
     */
    const handleClose = () => {
      dialogVisible.value = false
    }

    /**
     * 重置搜索表单
     */
    const resetSearchForm = () => {
      Object.assign(searchForm, {
        provinceId: '',
        cityId: '',
        name: '',
        code: ''
      })
      selectedCity.value = []
      pagination.pageIndex = 1
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        // 重置搜索表单并加载数据
        resetSearchForm()
        loadDataList()
      }
    })

    // 组件挂载时加载选项数据
    onMounted(() => {
      loadCityOptions()
    })

    return {
      dialogVisible,
      dataList,
      selectedCity,
      cityList,
      searchForm,
      pagination,
      handleCascaderChange,
      handleSearch,
      handlePageSizeChange,
      handlePageChange,
      handleRowClick,
      handleSelectItem,
      handleClose
    }
  }
}
</script>

 