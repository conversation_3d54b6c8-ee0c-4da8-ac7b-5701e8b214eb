import axios from '@/utils/axios'

/**
 * 选择器相关API
 */
export const selectorApi = {
  /**
   * 获取字典选项
   * @param {string} parentCode 父级字典编码
   * @returns {Promise} API响应，返回字典选项列表
   */
  getDicts(parentCode) {
    return axios.get('/Selector/GetDicts', {
      params: { parentCode }
    })
  },

  /**
   * 获取枚举选项
   * @param {Object} filter 枚举过滤条件
   * @returns {Promise} API响应，返回枚举选项列表
   */
  getEnumInfos(filter = {}) {
    return axios.get('/Selector/GetEnumInfos', {
      params: filter
    })
  },

  /**
   * 获取经销商选项
   * @returns {Promise} API响应，返回经销商选项列表
   */
  QueryReceiverSelectors(filter = {}) {
    return axios.get('/Selector/QueryReceiverSelectors', {
      params: filter
    })
  },

  /**
   * 获取省份城市选项
   * @returns {Promise} API响应，返回省份城市选项列表
   */
  ProvinceCitySelect() {
    return axios.get('/SysCache/QueryProvinceCityCascader')
  },

  /**
   * 获取省份城市区县选项
   * @returns {Promise} API响应，返回省份城市区县选项列表
   */
  ProvinceCityCountySelect() {
    return axios.get('/SysCache/QueryProvinceCityCountyCascader')
  },

  /**
   * 获取收货方类型选项
   * @returns {Promise} API响应，返回收货方类型选项列表
   */
  ReceiverTypeSelect() {
    return axios.get('/SysCache/QueryReceiverTypeCascader')
  },
  
  /**
   * 获取货主详情
   * @param {string} id 货主ID
   * @returns {Promise} API响应
   */
  getShipperSelectors() {
    return axios.get('/Shipper/GetShipperSelectors');
  }
}

export default selectorApi