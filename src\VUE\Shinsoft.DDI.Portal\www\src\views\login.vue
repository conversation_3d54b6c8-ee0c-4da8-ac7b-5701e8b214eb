<!--人员管理 作者：-->
<template>
  <div class="login">
    <div class="login-con1"></div>
    <div class="login-con2"></div>
    <div class="login-con">
      <div class="Ctop">
        <h1>药品销售数据采集平台</h1>
        <span style="font-size: 14px; font-weight: 500; color: #80848f; margin-top: 4px; display: block;"
          >Distributors Data Integration System</span
        >
      </div>
      <div class="form-con form-con-Sty">
        <el-form ref="loginForm" :model="loginForm" :rules="rules">
          <!-- 账号 -->
          <el-form-item prop="Username">
            <el-input
              v-model="loginForm.Username"
              :placeholder="$t('system.accountNumber')"
              size="small"
              style="width: 100%; height: 32px;"
            />
          </el-form-item>
          <!-- 密码 -->
          <el-form-item prop="Password" style="margin-top: 15px">
            <el-input
              type="password"
              v-model="loginForm.Password"
              :placeholder="$t('system.password')"
              size="small"
              style="width: 100%; height: 32px;"
            />
          </el-form-item>
          <!-- Vue 3滑块验证码组件 -->
          <el-form-item style="margin-top: 12px">
            <div class="vcode-container">
              <Vcode
                :show="isShowVcode"
                @success="onVerifySuccess"
                @close="onVerifyClose"
                :accuracy="5"
                :barSize="{
                  width: '100%',
                  height: '28px'
                }"
                :barText="{
                  sliding: '请按住滑块，拖动到最右边',
                  error: '验证失败，请重新操作',
                  success: '验证成功'
                }"
                :panelSize="{
                  width: '300px',
                  height: '140px'
                }"
                :puzzleSize="{
                  width: '50px',
                  height: '50px'
                }"
              />
              <el-button
                v-if="!isCheckPass"
                @click="showVcode"
                class="vcode-trigger-btn"
                type="primary"
                size="small"
              >
                <i class="el-icon-lock" style="margin-right: 5px;"></i>
                点击进行滑块验证
              </el-button>
              <div
                v-else
                class="vcode-success-status"
              >
                <i class="el-icon-check" style="margin-right: 5px;"></i>
                验证通过
              </div>
            </div>
          </el-form-item>
          <!-- 登录 -->
          <el-form-item style="margin-top: 15px">
            <el-button
              :disabled="!this.isCheckPass"
              @click="onSubmit('loginForm')"
              type="primary"
              size="small"
              style="
                background: #fd9e00 !important;
                border-color: #fd9e00 !important;
                color: #ffffff !important;
                width: 100% !important;
                font-size: 13px;
                height: 32px;
              "
              >{{ $t("system.logIn") }}</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <footer class="footer"><p></p></footer>
    <el-col class="demo-spin-col" :span="24" v-if="demoSpinColShow" v-loading="true">
    </el-col>
    <el-dialog
      v-model="showUpdatePasswordModal"
      :close-on-click-modal="false"
      width="500px"
      @close="handleCancelUpdatePassword"
      :title="$t('userlist.updatePassword')"
    >
    <div class="el-row-padding" style="height: 250px;">
      <el-form :label-width="90"
      ref="updatePasswordForm"
      :model="userModel"
      :rules="passwordRuleValidate">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('userlist.oldPassword')"  prop="OldPassword">
              <el-input type="password" v-model="userModel.OldPassword" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('userlist.newPassword')"  prop="NewPassword">
              <el-input type="password" v-model="userModel.NewPassword" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('userlist.pwdLevel')"  >
            <div class="input_span">
                  <span ref="refOne" class="one"></span>
                  <span  ref="refTwo" class="two"></span>
                  <span  ref="refThree" class="three"></span>
            </div>
            <div id="font">
                  <span>弱</span>
                  <span>中</span>
                  <span>强</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('userlist.confirmPassword')"  prop="ConfirmPassword">
              <el-input type="password" v-model="userModel.ConfirmPassword" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
      <template #footer>
        <el-row>
        <el-col :span="24">
          <el-row type="flex" justify="end" :gutter="10">
            <el-col :span="6">
              <el-button
               :disabled="!(this.pwdLevel>=3)"
                @click="handleUpdatePassword()"
                type="primary"
                >{{ $t("system.save") }}</el-button
              >
            </el-col>
            <el-col :span="6">
              <el-button
                @click="handleCancelUpdatePassword()"
                style="margin-left: 8px"
                >{{ $t("system.cancel") }}</el-button
              >
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      </template>
    </el-dialog>
  </div>
</template>
<script>
    import Vcode from 'vue3-puzzle-vcode'

    export default {
        components: {
            Vcode
        },
        data () {
            const validatePassword = (rule, value, callback) => {
                if (value !== this.userModel.NewPassword) {
                    callback(new Error(this.$t('userEdit.validPasswordCompare')))
                } else {
                    callback()
                }
            }
            return {
                isCheckPass: false, // 需要通过验证码验证
                isShowVcode: false, // 控制验证码显示
                demoSpinColShow: false,

                loginForm: {
                    Username: null,
                    Password: null
                },
                showUpdatePasswordModal: false,
                rules: {
                    // 账号不能为空
                    Username: [
                        {
                            required: true,
                            message: this.$t('system.accountCannotBeEmpty'),
                            trigger: 'blur'
                        }
                    ],
                    // 密码不能为空
                    Password: [
                        {
                            required: true,
                            message: this.$t('system.passwordBeEmpty'),
                            trigger: 'blur'
                        }
                    ]
                },
                userModel: {},
                pwdLevel: 0,
                passwordRuleValidate: {
                    OldPassword: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validOldPassword'),
                            trigger: 'blur,change'
                        }
                    ],
                    NewPassword: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validNewPassword'),
                            trigger: 'blur,change'
                        }
                    ],
                    ConfirmPassword: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validConfirmPassword'),
                            trigger: 'blur,change'
                        },
                        {validator: validatePassword}
                    ]
                }
            };
        },
        created () {
            if (this.$route.params.code !== undefined) {
                this.$Message.destroy();
                this.$Message.error({
                    content: this.$t(this.$route.params.code),
                    duration: 10
                });
            }
            // 点击键盘 Enter键---执行登陆
            var _this = this;
            document.onkeydown = function (e) {
                var key = window.event.keyCode;
                if (key === 13) {
                    _this.onSubmit('loginForm');
                }
            };
        },
        mounted() {
            // 不在mounted中自动显示验证码，只在用户点击时显示
        },
        beforeUnmount() {
            // Vue 3 中使用 beforeUnmount 替代 beforeDestroy
            // 清理资源
        },
        methods: {
            // 显示验证码
            showVcode() {
                this.isShowVcode = true;
            },

            // 验证成功回调
            onVerifySuccess() {
                this.isCheckPass = true;
                this.isShowVcode = false;
                console.log('验证码验证成功');
            },
            // 关闭验证码
            onVerifyClose() {
                this.isShowVcode = false;
            },
            // 兼容原有的verify方法
            verify (val) {
                this.isCheckPass = val;
            },
            handleCancelUpdatePassword () {
                this.pwdLevel = 0;
                this.$refs.updatePasswordForm.resetFields()
                this.showUpdatePasswordModal = false;
            },
            handleUpdatePassword () {
                this.$refs['updatePasswordForm'].validate((valid) => {
                    if (valid) {
                        this.$http.post('/User/UpdatePassword', this.userModel).then(response => {
                            if (response.data.Message) {
                                this.$alert(response.data.Message, this.$t('system.alter'), {
                                    dangerouslyUseHTMLString: true
                                });
                            } else {
                                this.$Message.success(this.$t('system.saveSuccess'));
                                this.handleCancelUpdatePassword()
                            }
                        })
                    }
                })
            },
            checkStrong (sValue) {
                var modes = 0;
                // 正则表达式验证符合要求的
                if (sValue.length < 1) return modes;
                if (/\d/.test(sValue)) modes++; // 数字
                if (/[a-z]/.test(sValue)) modes++; // 小写
                if (/[A-Z]/.test(sValue)) modes++; // 大写
                if (/\W/.test(sValue)) modes++; // 特殊字符

                // 逻辑处理
                switch (modes) {
                case 1:
                    return 1;
                case 2:
                    return 2;
                case 3:
                    return sValue.length < 8 ? 2 : 3;
                case 4:
                    return sValue.length < 8 ? 2 : 4;
                }
                return modes;
            },
            onSubmit (name) {
                sessionStorage.demoSpinIconLoad = 'false';
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.demoSpinColShow = true;
                        // 修正API路径和参数格式
                        const loginData = {
                            LoginName: this.loginForm.Username,
                            Password: this.loginForm.Password
                        };
                        this.$http
                            .post('/Sys/UserLogin', loginData)
                            .then((response) => {
                                // 添加调试信息
                                console.log('登录响应数据:', response.data);

                                // 检查响应结构，后台返回的是BizResult<IdentityUser>
                                if (response.data && response.data.success && response.data.data) {
                                    const userData = response.data.data;

                                    // 检查是否需要修改密码（如果有这个字段）
                                    if (userData.daysSinceLastPasswordChange >= 90) {
                                        this.userModel.LoginName = userData.username || this.loginForm.Username;
                                        this.showUpdatePasswordModal = true;
                                        this.demoSpinColShow = false;
                                        return;
                                    }

                                    // 保存Token和基本用户信息
                                    if (userData.authToken) {
                                        // AuthToken已经包含"Bearer "前缀，直接保存
                                        localStorage.token = userData.authToken.replace('Bearer ', '');
                                        localStorage.tokenStorage = 'tokenStorage';
                                        localStorage.userId = userData.userId;
                                        localStorage.shipperId = userData.shipperId;
                                        localStorage.shipperName = userData.shipperName;
                                        localStorage.account = userData.loginName || this.loginForm.Username;
                                        localStorage.name = userData.displayName || userData.loginName || this.loginForm.Username;
                                        // 保存权限信息（如果有的话）
                                        if (userData.permission) {
                                            localStorage.permission = JSON.stringify(userData.permission);
                                        }

                                        // 直接跳转到首页，不再调用getModule
                                        this.$router.push({
                                            name: 'index'
                                        });
                                    } else {
                                        this.$message.error('登录失败：未获取到AuthToken');
                                    }
                                } else {
                                    // 显示错误信息
                                    const errorMsg = response.data?.messages?.[0] || response.data?.message || '登录失败，请检查用户名和密码';
                                    this.$message.error(errorMsg);
                                }
                                this.demoSpinColShow = false;
                            })
                            .catch((error) => {
                                console.error('登录请求失败:', error);
                                this.$message.error('网络错误，请稍后重试');
                                this.demoSpinColShow = false;
                            });
                    }
                });
            },

        },
        watch: {
            'userModel.NewPassword': {
                handler (newname, oldname) {
                    if (newname === null || newname === undefined) {
                        this.$refs.refOne.style.background = '#eee';
                        this.$refs.refTwo.style.background = '#eee';
                        this.$refs.refThree.style.background = '#eee';
                    } else {
                        this.pwdLevel = this.checkStrong(newname);
                        if (this.pwdLevel > 1 || this.pwdLevel === 1) {
                            this.$refs.refOne.style.background = 'red'
                        } else {
                            this.$refs.refOne.style.background = '#eee'
                        }
                        if (this.pwdLevel > 2 || this.pwdLevel === 2) {
                            this.$refs.refTwo.style.background = 'red';
                        } else {
                            this.$refs.refTwo.style.background = '#eee';
                        }
                        if (this.pwdLevel > 3 || this.pwdLevel === 3) {
                            this.$refs.refTwo.style.background = 'orange';
                        } else {
                            this.$refs.refTwo.style.background = '#eee';
                        }
                        if (this.pwdLevel === 4) {
                            this.$refs.refThree.style.background = '#00D1B2';
                        } else {
                            this.$refs.refThree.style.background = '#eee';
                        }
                    }
                }
            }
        }
    };
</script>
<style>
.verify-btn {
    display: none !important;
}
.form-con {
  width: 100%;
  margin-top: 12px;
}
.form-con-Sty .el-input__inner {
  height: 32px;
  line-height: 32px;
  font-size: 12px;
}
</style>
<style scoped>
.login {
  width: 100vw;
  height: 100vh;
  min-height: 460px;
  background: #fff;
  position: relative;
  overflow: auto;
}

.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  line-height: 60px;
}

.footer p {
  text-align: center;
  color: #003264;
  font-size: 16px;
}

.login-con {
  position: absolute;
  width: 320px;
  height: auto;
  top: 50%;
  transform: translateY(-50%);
  left: 75%;
  margin-left: -160px;
  z-index: 4;
  padding: 18px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.login-con1 {
  position: absolute;
  width: 200px;
  height: 80px;
  top: 0px;
  margin-top: 0px;
  z-index: 2;
  background-image: url("../assets/cdms_logo.png");
  background-size:contain;
  background-repeat: no-repeat;
}

.login-con2 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  margin-top: 0px;
  z-index: 3;
  background-image: url("../assets/login_bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  opacity: 0.3;
}



.Ctop img {
  width: 20%;
}

.Ctop h3,
.Ctop h2,
.Ctop h1 {
  color: #fd9e00;
}

h1 {
  font-size: 24px;
  margin-bottom: 6px;
  line-height: 36px;
}

h2 {
  font-size: 32px !important;
}

.Cbody {
  position: absolute;
  top: 50%;
  width: 85%;
  margin-top: -70px;
}

.verify-bar-area .verify-move-block:hover {
    background-color: #bec418 !important;
    color: #FFFFFF;
}
.el-input__inner {
  display: inline-block;
  height: 32px;
  line-height: 32px;
  padding-left: 10px;
  font-size: 12px;
  border-radius: 5px;
  color: #495060;
  background: #ebefee;
}

.el-form-item {
  margin-bottom: 12px !important;
}

/* 调整输入框内部样式 */
.form-con-Sty .el-input__inner {
  height: 32px !important;
  line-height: 32px !important;
  font-size: 12px !important;
  padding: 0 10px !important;
}

/* 调整输入框占位符文字大小 */
.el-input__inner::placeholder {
  font-size: 12px !important;
  color: #c0c4cc !important;
}

/* Element Plus按钮样式修复 */
.el-button--primary {
  background-color: #fd9e00 !important;
  border-color: #fd9e00 !important;
  color: #ffffff !important;
}

.el-button--primary:hover {
  background-color: #e88900 !important;
  border-color: #e88900 !important;
}

.el-button--primary:focus {
  background-color: #fd9e00 !important;
  border-color: #fd9e00 !important;
}

/* 验证码容器样式 */
.vcode-container {
  width: 100%;
  position: relative;
}

/* 验证码触发按钮样式 */
.vcode-trigger-btn {
  width: 100% !important;
  height: 28px !important;
  background: #fef7ec !important;
  border: 1px solid #fcd9a6 !important;
  color: #fd9e00 !important;
  font-size: 12px !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.vcode-trigger-btn:hover {
  background: #fdf2ec !important;
  border-color: #fd9e00 !important;
  color: #e88900 !important;
}

/* 验证成功状态样式 */
.vcode-success-status {
  width: 100%;
  height: 28px;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #67c23a;
  border-radius: 4px;
  color: #67c23a;
  font-size: 12px;
  font-weight: 500;
}

/* 验证码组件样式自定义 - 与系统橙色主题保持一致 */
.vcode-drop-btn {
  background: #fd9e00 !important;
  border-color: #fd9e00 !important;
  color: #ffffff !important;
}

.vcode-drop-btn:hover {
  background: #e88900 !important;
  border-color: #e88900 !important;
}

/* 验证码组件主题色覆盖 */
.vcode-drop .vcode-drop-box .vcode-img-panel .panel-action .action-close {
  color: #fd9e00 !important;
}

.vcode-drop .vcode-drop-box .vcode-img-panel .panel-action .action-close:hover {
  color: #e88900 !important;
  background: rgba(253, 158, 0, 0.1) !important;
}

/* 滑块样式 */
.vcode-drag-bar {
  background: #f8f8f9 !important;
  border: 1px solid #dcdee2 !important;
  border-radius: 4px !important;
  height: 36px !important;
  line-height: 34px !important;
}

.vcode-drag-bar .drag-bar-bg {
  background: linear-gradient(to right, #fd9e00, #ff9900) !important;
  border-radius: 3px !important;
}

.vcode-drag-bar .drag-bar-text {
  color: #495060 !important;
  font-size: 13px !important;
  line-height: 34px !important;
}

.vcode-drag-bar .drag-bar-btn {
  background: #fd9e00 !important;
  border: 1px solid #fd9e00 !important;
  color: #ffffff !important;
  width: 34px !important;
  height: 34px !important;
  border-radius: 3px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.vcode-drag-bar .drag-bar-btn:hover {
  background: #e88900 !important;
  border-color: #e88900 !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

/* 验证成功状态 */
.vcode-drag-bar.success .drag-bar-bg {
  background: linear-gradient(to right, #19be6b, #19be6b) !important;
}

.vcode-drag-bar.success .drag-bar-btn {
  background: #19be6b !important;
  border-color: #19be6b !important;
}

/* 验证失败状态 */
.vcode-drag-bar.error .drag-bar-bg {
  background: linear-gradient(to right, #ed4014, #ed4014) !important;
}

.vcode-drag-bar.error .drag-bar-btn {
  background: #ed4014 !important;
  border-color: #ed4014 !important;
}

/* 拼图验证区域 */
.vcode-img-panel {
  border: 1px solid #dcdee2 !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1) !important;
  overflow: hidden !important;
}

.vcode-img-panel .img-refresh {
  color: #fd9e00 !important;
  font-size: 16px !important;
  padding: 8px !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.vcode-img-panel .img-refresh:hover {
  color: #e88900 !important;
  background: rgba(253, 158, 0, 0.1) !important;
}

/* 验证码弹窗位置调整 */
.vcode-drop {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
}

.vcode-drop .vcode-drop-box {
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
}

/* 关闭按钮 */
.vcode-close {
  color: #80848f !important;
}

.vcode-close:hover {
  color: #495060 !important;
}

/* 深度样式覆盖 - 确保所有蓝色元素都变为橙色 */

/* 使用深度选择器确保样式生效 - 橙色主题 */
:deep(.vcode-drop-box) {
  border: 1px solid #fd9e00 !important;
}

/* 弹出框内的刷新按钮 */
:deep(.vcode-img-panel .panel-action .action-refresh) {
  color: #fd9e00 !important;
  background: rgba(253, 158, 0, 0.1) !important;
  border-radius: 4px !important;
  padding: 4px !important;
}

:deep(.vcode-img-panel .panel-action .action-refresh:hover) {
  color: #e88900 !important;
  background: rgba(253, 158, 0, 0.2) !important;
}

/* 弹出框内的关闭按钮 */
:deep(.vcode-img-panel .panel-action .action-close) {
  color: #fd9e00 !important;
  background: rgba(253, 158, 0, 0.1) !important;
  border-radius: 4px !important;
  padding: 4px !important;
}

:deep(.vcode-img-panel .panel-action .action-close:hover) {
  color: #e88900 !important;
  background: rgba(253, 158, 0, 0.2) !important;
}

/* 滑块验证条内的文字颜色 */
:deep(.vcode-drag-bar .drag-bar-text) {
  color: #495060 !important;
}

/* 滑块按钮样式 */
:deep(.vcode-drag-bar .drag-bar-btn) {
  background: #fd9e00 !important;
  border-color: #fd9e00 !important;
  box-shadow: 0 2px 4px rgba(253, 158, 0, 0.3) !important;
}

:deep(.vcode-drag-bar .drag-bar-btn:hover) {
  background: #e88900 !important;
  border-color: #e88900 !important;
  box-shadow: 0 2px 8px rgba(253, 158, 0, 0.4) !important;
}

/* 滑块进度条背景 */
:deep(.vcode-drag-bar .drag-bar-bg) {
  background: linear-gradient(to right, #fd9e00, #ff9900) !important;
}

/* 滑块滑动时的底色 - 覆盖蓝色 */
:deep(.vcode-drag-bar .drag-bar-bg.active) {
  background: linear-gradient(to right, #fd9e00, #ff9900) !important;
}

:deep(.vcode-drag-bar .drag-bar-bg.moving) {
  background: linear-gradient(to right, #fd9e00, #ff9900) !important;
}

/* 滑块条整体背景色 */
:deep(.vcode-drag-bar) {
  background-color: #f8f8f9 !important;
}

/* 滑块按钮在滑动状态下的样式 */
:deep(.vcode-drag-bar .drag-bar-btn.active) {
  background: #fd9e00 !important;
  border-color: #fd9e00 !important;
  box-shadow: 0 2px 8px rgba(253, 158, 0, 0.4) !important;
}

:deep(.vcode-drag-bar .drag-bar-btn.moving) {
  background: #fd9e00 !important;
  border-color: #fd9e00 !important;
  box-shadow: 0 2px 8px rgba(253, 158, 0, 0.4) !important;
}

/* 验证成功状态 */
:deep(.vcode-drag-bar.success .drag-bar-bg) {
  background: linear-gradient(to right, #67c23a, #85ce61) !important;
}

:deep(.vcode-drag-bar.success .drag-bar-btn) {
  background: #67c23a !important;
  border-color: #67c23a !important;
}

:deep(.vcode-drag-bar.success .drag-bar-text) {
  color: #67c23a !important;
}

/* 验证失败状态 */
:deep(.vcode-drag-bar.error .drag-bar-bg) {
  background: linear-gradient(to right, #ed4014, #f56c6c) !important;
}

:deep(.vcode-drag-bar.error .drag-bar-btn) {
  background: #ed4014 !important;
  border-color: #ed4014 !important;
}

:deep(.vcode-drag-bar.error .drag-bar-text) {
  color: #ed4014 !important;
}

/* 全局验证码组件样式覆盖 */
.vcode-drop {
  --primary-color: #fd9e00;
  --primary-hover-color: #e88900;
  --success-color: #67c23a;
  --error-color: #ed4014;
}

/* 覆盖组件内部可能使用的CSS变量 */
.vcode-container {
  --vcode-primary-color: #fd9e00;
  --vcode-primary-light: #ff9900;
  --vcode-primary-dark: #e88900;
}

/* 使用!important强制覆盖内联样式 */
.vcode-container :deep(.drag-bar-bg),
.vcode-container :deep(.drag-bar-bg *) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
}

/* 强制覆盖所有可能的蓝色样式 */
:deep(.vcode-drop-box .vcode-img-panel) {
  border-color: #dcdee2 !important;
}

:deep(.vcode-drop-box .vcode-drag-bar) {
  border-color: #dcdee2 !important;
  background-color: #f8f8f9 !important;
}

/* 拼图块的边框颜色 */
:deep(.vcode-drop-box .vcode-img-panel .img-box .img-item) {
  border-color: #fd9e00 !important;
}

/* 拼图缺口的颜色 */
:deep(.vcode-drop-box .vcode-img-panel .img-box .puzzle-piece) {
  border-color: #fd9e00 !important;
  box-shadow: 0 0 10px rgba(253, 158, 0, 0.3) !important;
}

/* 强制覆盖所有可能的蓝色样式 */
:deep(.vcode-drag-bar .drag-bar-bg) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 滑块在不同状态下的颜色 */
:deep(.vcode-drag-bar.dragging .drag-bar-bg) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

:deep(.vcode-drag-bar.verifying .drag-bar-bg) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 覆盖任何可能的蓝色背景 */
:deep(.vcode-drag-bar .drag-bar-bg::before) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

:deep(.vcode-drag-bar .drag-bar-bg::after) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 滑块按钮的所有状态 */
:deep(.vcode-drag-bar .drag-bar-btn) {
  background: #fd9e00 !important;
  border: 1px solid #fd9e00 !important;
  color: #ffffff !important;
}

:deep(.vcode-drag-bar .drag-bar-btn:active) {
  background: #e88900 !important;
  border-color: #e88900 !important;
}

/* 使用更高优先级的选择器 */
.vcode-container :deep(.vcode-drag-bar .drag-bar-bg) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 强制覆盖所有可能的蓝色样式 - 使用更高优先级 */
.vcode-drop :deep(.vcode-drag-bar .drag-bar-bg),
.vcode-drop :deep(.vcode-drag-bar .drag-bar-bg *),
.vcode-drop :deep(.vcode-drag-bar .drag-bar-bg::before),
.vcode-drop :deep(.vcode-drag-bar .drag-bar-bg::after) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
}

/* 覆盖内联样式 */
.vcode-drop :deep([style*="background"]) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 覆盖可能的CSS类名 */
.vcode-drop :deep(.bg-primary),
.vcode-drop :deep(.primary-bg),
.vcode-drop :deep(.blue-bg) {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 全局样式覆盖 - 确保滑块组件完全使用橙色 */
.vcode-drop * {
  --primary-color: #fd9e00 !important;
  --primary-light: #ff9900 !important;
  --primary-dark: #e88900 !important;
}

/* 使用属性选择器强制覆盖 */
.vcode-drop [class*="drag-bar-bg"] {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

.vcode-drop [class*="progress"] {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

.vcode-drop [style*="rgb(64, 158, 255)"] {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

.vcode-drop [style*="#409eff"] {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 使用更强制的全局样式覆盖 */
body .vcode-drop .vcode-drag-bar .drag-bar-bg,
html .vcode-drop .vcode-drag-bar .drag-bar-bg {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
}

/* 覆盖可能的蓝色值 */
.vcode-drop * {
  color: inherit !important;
}

.vcode-drop .vcode-drag-bar .drag-bar-bg {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
  background-image: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 使用JavaScript动态修改样式的备用方案 */

/* 最高优先级的样式覆盖 - 使用!important和多重选择器 */
html body .login .vcode-drop .vcode-drag-bar .drag-bar-bg,
html body .login .vcode-container .vcode-drop .vcode-drag-bar .drag-bar-bg,
html body .login .vcode-drop .vcode-drag-bar .drag-bar-bg[style],
html body .login .vcode-drop .vcode-drag-bar .drag-bar-bg[style*="background"] {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
  background-image: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 覆盖可能的内联样式 */
.login .vcode-drop [style*="rgb(64, 158, 255)"],
.login .vcode-drop [style*="#409eff"],
.login .vcode-drop [style*="blue"] {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
}
</style>

<!-- 添加全局样式来强制覆盖滑块组件 -->
<style>
/* 全局样式 - 最高优先级覆盖滑块验证组件的蓝色 */
.vcode-drop .vcode-drag-bar .drag-bar-bg,
.vcode-drop .vcode-drag-bar .drag-bar-bg * {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
  background-image: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 覆盖所有可能的蓝色样式 */
.vcode-drop [style*="rgb(64, 158, 255)"],
.vcode-drop [style*="#409eff"],
.vcode-drop [style*="background: rgb(64, 158, 255)"],
.vcode-drop [style*="background-color: rgb(64, 158, 255)"] {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
}

/* 使用属性选择器强制覆盖 */
.vcode-drop div[style*="background"] {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 为JavaScript动态添加的类提供样式 */
.orange-progress-bar {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
  background-color: #fd9e00 !important;
  background-image: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

/* 基本的滑块组件样式覆盖 */
.vcode-drop .vcode-drag-bar .drag-bar-bg {
  background: linear-gradient(90deg, #fd9e00 0%, #ff9900 100%) !important;
}

.vcode-drop .vcode-drag-bar .drag-bar-btn {
  background: #fd9e00 !important;
  border-color: #fd9e00 !important;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(360deg);
  }
}
.demo-spin-col {
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  position: absolute;
  border: 1px solid #eee;
  z-index: 9999;
}
.input_span span {
display: inline-block;
width: 85px;
height: 10px;
background: #eee;
line-height: 20px;
}
.one {
border-top-left-radius: 5px;
border-bottom-left-radius: 5px;
border-right: 0px solid;
margin-left: 20px;
margin-right: 3px;
}

.two {
border-left: 0px solid;
border-right: 0px solid;
margin-left: -5px;
margin-right: 3px;
}

.three {
border-top-right-radius: 5px;
border-bottom-right-radius: 5px;
border-left: 0px solid;
margin-left: -5px;
}
#font span:nth-child(1){
color:red;
margin-left: 80px;
}
#font span:nth-child(2){
    color:orange;
    margin: 0 60px;
  }
#font span:nth-child(3){
     color:#00D1B2;
  }
</style>
