import axios from '@/utils/axios'

/**
 * 货主相关API
 */
export const managementApi = {
  /**
   * 查询客户端日志列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.receiverCode 客户编码
   * @param {string} params.receiverName 客户名称
   * @param {string} params.message 日志内容
   * @param {Array<string>} params.logTimeRange 日志时间范围
   * @param {string} params.order 排序
   * @returns {Promise} API响应
   */
  queryReceiverClientLog(params) {
    return axios.get('/Management/QueryReceiverClientLog', { params })
  },

}

export default managementApi