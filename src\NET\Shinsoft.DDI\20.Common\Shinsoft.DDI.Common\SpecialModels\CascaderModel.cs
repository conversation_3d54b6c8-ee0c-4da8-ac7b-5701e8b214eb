﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Common
{
    public class CascaderModel
    {
        [Description("Label")]
        public string? Label { get; set; }

        [Description("Value")]
        public Guid Value { get; set; }

        [Description("Code")]
        public string? Code { get; set; }

        [Description("ParentId")]
        public Guid? ParentId { get; set; }

        [Description("Children")]
        public List<CascaderModel>? Children { get; set; }
    }
}
