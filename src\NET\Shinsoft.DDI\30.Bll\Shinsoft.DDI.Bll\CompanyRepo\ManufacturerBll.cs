using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.ComponentModel;

namespace Shinsoft.DDI.Bll
{
    /// <summary>
    /// 药企业务逻辑层
    /// 提供药企相关的业务操作，包括增删改查、数据验证等功能
    /// </summary>
    [Description("药企业务逻辑层")]
    public class ManufacturerBll : BaseBll
    {
        #region Constructs

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUser">操作用户</param>
        public ManufacturerBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUniqueName">操作用户唯一名称</param>
        public ManufacturerBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="operatorUser">操作用户</param>
        public ManufacturerBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="bll">业务逻辑层接口</param>
        public ManufacturerBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region Manufacturer 药企管理

        /// <summary>
        /// 添加药企
        /// </summary>
        /// <param name="entity">药企实体</param>
        /// <returns>操作结果</returns>
        public BizResult<Manufacturer> AddManufacturer(Manufacturer entity)
        {
            var result = new BizResult<Manufacturer>();

            // 验证必填字段
            if (entity.Name.IsEmpty())
            {
                result.Error("请输入厂家名称");
            }
            else
            {
                // 检查名称是否重复
                var existByName = this.GetEntity<Manufacturer>(p => p.Name == entity.Name);
                if (existByName != null)
                {
                    result.Error("已存在相同名称的药企");
                }
            }

            if (result.Success)
            {
                if (entity.Code.IsEmpty())
                {
                    // 自动生成编码
                    entity.Code = GetSerialNumber(ConstDefinition.CodePrefix.Manufacturer_CodePrefix);
                }
                else
                {
                    // 检查编码是否重复
                    var existByCode = this.GetEntity<Manufacturer>(p => p.Code == entity.Code);
                    if (existByCode != null)
                    {
                        result.Error("已存在相同编码的药企");
                    }
                }
            }

            if (result.Success)
            {
                entity.ID = CombGuid.NewGuid();
                entity.EnumStatus = ManufacturerStatus.Valid;

                entity = this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 更新药企
        /// </summary>
        /// <param name="entity">药企实体</param>
        /// <returns>操作结果</returns>
        public BizResult<Manufacturer> UpdateManufacturer(Manufacturer entity)
        {
            var result = new BizResult<Manufacturer>();

            var dbEntity = this.Get<Manufacturer>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("药企不存在");
            }
            else
            {
                // 验证必填字段
                if (entity.Name.IsEmpty())
                {
                    result.Error("请输入厂家名称");
                }
                else
                {
                    // 检查名称是否重复
                    var existByName = this.GetEntity<Manufacturer>(p => p.Name == entity.Name && p.ID != entity.ID);
                    if (existByName != null)
                    {
                        result.Error("已存在相同名称的药企");
                    }
                }
            }

            if (result.Success)
            {
                if (!entity.Code.IsEmpty())
                {
                    // 检查编码是否重复
                    var existByCode = this.GetEntity<Manufacturer>(p => p.Code == entity.Code && p.ID != entity.ID);
                    if (existByCode != null)
                    {
                        result.Error("已存在相同编码的药企");
                    }
                }
            }

            if (result.Success)
            {
                this.Update(entity);

                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 删除药企
        /// </summary>
        /// <param name="id">药企ID</param>
        /// <returns>操作结果</returns>
        public BizResult DeleteManufacturer(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<Manufacturer>(id);
            if (entity == null)
            {
                result.Error("药企不存在");
            }
            else
            {
                // 检查是否有关联的产品
                var hasProducts = this.GetEntity<Product>(p => p.ManufacturerId == id);
                if (hasProducts != null)
                {
                    result.Error("该药企下存在产品，无法删除");
                }
                else
                {
                    this.Delete(entity);
                }
            }

            return result;
        }

        /// <summary>
        /// 停用药企
        /// </summary>
        /// <param name="id">药企ID</param>
        /// <returns>操作结果</returns>
        public BizResult StopManufacturer(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<Manufacturer>(id);
            if (entity == null)
            {
                result.Error("药企不存在");
            }
            else
            {
                //TODO 处理业务逻辑
                entity.EnumStatus = ManufacturerStatus.Stoped;
                this.Update(entity);
            }

            return result;
        }

        /// <summary>
        /// 启用药企
        /// </summary>
        /// <param name="id">药企ID</param>
        /// <returns>操作结果</returns>
        public BizResult EnableManufacturer(Guid id)
        {
            var result = new BizResult();

            var entity = this.Get<Manufacturer>(id);
            if (entity == null)
            {
                result.Error("药企不存在");
            }
            else
            {
                //TODO 处理业务逻辑

                entity.EnumStatus = ManufacturerStatus.Valid;
                this.Update(entity);
            }

            return result;
        }

        /// <summary>
        /// 获取药企详情
        /// </summary>
        /// <param name="id">药企ID</param>
        /// <returns>药企实体</returns>
        public Manufacturer? GetManufacturer(Guid id)
        {
            return this.GetEntity<Manufacturer>(p => p.ID == id);
        }

        #endregion Manufacturer 药企管理
    }
}