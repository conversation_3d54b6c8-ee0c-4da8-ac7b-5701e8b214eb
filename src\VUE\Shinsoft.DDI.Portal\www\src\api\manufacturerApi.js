import axios from '@/utils/axios'

/**
 * 药企管理相关API
 * 提供药企的增删改查等功能
 */
export const manufacturerApi = {
  /**
   * 分页查询药企列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.name 药企名称
   * @param {string} params.order 排序
   * @returns {Promise} API响应
   */
  queryManufacturer(params) {
    return axios.get('/Manufacturer/QueryManufacturer', { params })
  },

  /**
   * 获取药企详情
   * @param {string} id 药企ID
   * @returns {Promise} API响应
   */
  getManufacturer(id) {
    return axios.get('/Manufacturer/Get', { params: { id } })
  },

  /**
   * 新增药企
   * @param {Object} data 药企数据
   * @param {string} data.name 药企名称
   * @param {string} data.shortName 药企简称
   * @param {string} data.country 国家
   * @param {string} data.remark 备注
   * @returns {Promise} API响应
   */
  addManufacturer(data) {
    return axios.post('/Manufacturer/Add', data)
  },

  /**
   * 编辑药企
   * @param {Object} data 药企数据
   * @param {string} data.id 药企ID
   * @param {string} data.code 药企编码
   * @param {string} data.name 药企名称
   * @param {string} data.shortName 药企简称
   * @param {string} data.country 国家
   * @param {number} data.enumStatus 状态
   * @param {string} data.remark 备注
   * @returns {Promise} API响应
   */
  editManufacturer(data) {
    return axios.post('/Manufacturer/Edit', data)
  },

  /**
   * 删除药企
   * @param {string} id 药企ID
   * @returns {Promise} API响应
   */
  deleteManufacturer(id) {
    return axios.post('/Manufacturer/Delete', { id })
  },

  /**
   * 获取药企选择器列表（不翻页）
   * @returns {Promise} API响应
   */
  getManufacturerSelector() {
    return axios.get('/Manufacturer/GetManufacturerSelector')
  },

  /**
   * 停用药企
   * @param {Object} data 药企数据
   * @returns {Promise} API响应
   */
  stopManufacturer(data) {
    return axios.post('/Manufacturer/StopManufacturer', data)
  },

  /**
   * 启用药企
   * @param {Object} data 药企数据
   * @returns {Promise} API响应
   */
  enableManufacturer(data) {
    return axios.post('/Manufacturer/EnableManufacturer', data)
  }
}

export default manufacturerApi
