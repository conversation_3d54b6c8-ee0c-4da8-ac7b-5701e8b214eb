-- =============================================
-- Portal项目路由数据插入脚本
-- 说明: 将Portal项目的静态路由转换为VueRoute和VueRouteMeta表数据
-- 注意: 请将@CompanyId替换为实际的公司ID
-- =============================================

-- 设置公司ID变量（请替换为实际的公司ID）
DECLARE @CompanyId UNIQUEIDENTIFIER = '00000000-0000-0000-0000-000000000000';

-- 开始事务
BEGIN TRANSACTION;

TRY

-- 1. 首页模块
DECLARE @HomeId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @HomeId, @CompanyId, NULL, 'home', '/home', '/home/<USER>',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @HomeId, @CompanyId, '首页', 'compose', 1,
    1, NULL, NULL, 1, '["Home:View"]'
);

-- 首页子路由
DECLARE @HomeIndexId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @HomeIndexId, @CompanyId, @HomeId, 'index', '/home/<USER>', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @HomeIndexId, @CompanyId, '首页', 'compose', 101,
    1, NULL, NULL, 1, '["Home:View"]'
);

-- 2. 主数据管理模块
DECLARE @MasterId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @MasterId, @CompanyId, NULL, 'master', '/master', '/master/shipper',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @MasterId, @CompanyId, '主数据管理', 'compose', 2,
    1, NULL, NULL, 1, NULL
);

-- 货主管理
DECLARE @ShipperId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @ShipperId, @CompanyId, @MasterId, 'shipperList', '/master/shipper', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @ShipperId, @CompanyId, '货主管理', 'compose', 201,
    1, NULL, NULL, 1, '["Master:Shipper:Query"]'
);

-- 收货方管理
DECLARE @DistributorId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DistributorId, @CompanyId, @MasterId, 'distributorList', '/master/distributor', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DistributorId, @CompanyId, '收货方管理', 'compose', 202,
    1, NULL, NULL, 1, '["Master:Distributor:Query"]'
);

-- 产品管理
DECLARE @ProductId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @ProductId, @CompanyId, @MasterId, 'productList', '/master/product', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @ProductId, @CompanyId, '产品管理', 'compose', 203,
    1, NULL, NULL, 1, '["Master:Product:Query"]'
);

-- 3. DDI配置监控模块
DECLARE @DDIConfigId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DDIConfigId, @CompanyId, NULL, 'ddi-config', '/ddi-config', '/ddi-config/config/distributor',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DDIConfigId, @CompanyId, 'DDI配置监控', 'compose', 3,
    1, NULL, NULL, 1, NULL
);

-- DDI配置父级
DECLARE @DDIConfigParentId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DDIConfigParentId, @CompanyId, @DDIConfigId, 'ddiConfig', '/ddi-config/config', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DDIConfigParentId, @CompanyId, 'DDI配置', 'compose', 301,
    1, NULL, NULL, 1, '["DDI:Config:View"]'
);

-- 经销商配置
DECLARE @DistributorConfigId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DistributorConfigId, @CompanyId, @DDIConfigId, 'distributorConfig', '/ddi-config/config/distributor', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DistributorConfigId, @CompanyId, '经销商配置', 'compose', 302,
    1, NULL, NULL, 1, '["DDI:Config:Distributor"]'
);

-- 产品配置
DECLARE @ProductConfigId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @ProductConfigId, @CompanyId, @DDIConfigId, 'productConfig', '/ddi-config/config/product', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @ProductConfigId, @CompanyId, '产品配置', 'compose', 303,
    1, NULL, NULL, 1, '["DDI:Config:Product"]'
);

-- 客户端配置
DECLARE @ClientConfigId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @ClientConfigId, @CompanyId, @DDIConfigId, 'clientConfig', '/ddi-config/config/client', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @ClientConfigId, @CompanyId, '客户端配置', 'compose', 304,
    1, NULL, NULL, 1, '["DDI:Config:Client"]'
);

-- DDI监控
DECLARE @DDIMonitorId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect], 
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DDIMonitorId, @CompanyId, @DDIConfigId, 'ddiMonitor', '/ddi-config/monitor', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank], 
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DDIMonitorId, @CompanyId, 'DDI监控', 'compose', 305,
    1, NULL, NULL, 1, '["DDI:Monitor:View"]'
);

-- 4. 数据清洗模块
DECLARE @SalesflowCleaningId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @SalesflowCleaningId, @CompanyId, NULL, 'salesflow-cleaning', '/salesflow-cleaning', '/salesflow-cleaning/alias/product',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @SalesflowCleaningId, @CompanyId, '数据清洗', 'compose', 4,
    1, NULL, NULL, 1, NULL
);

-- 产品别名
DECLARE @ProductAliasId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @ProductAliasId, @CompanyId, @SalesflowCleaningId, 'productAlias', '/salesflow-cleaning/alias/product', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @ProductAliasId, @CompanyId, '产品别名', 'compose', 401,
    1, NULL, NULL, 1, '["DataCleaning:ProductAlias:Query"]'
);

-- 收货方别名
DECLARE @DistributorAliasId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DistributorAliasId, @CompanyId, @SalesflowCleaningId, 'distributorAlias', '/salesflow-cleaning/alias/distributor', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DistributorAliasId, @CompanyId, '收货方别名', 'compose', 402,
    1, NULL, NULL, 1, '["DataCleaning:DistributorAlias:Query"]'
);

-- 流向导入日志
DECLARE @ImportSalesflowId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @ImportSalesflowId, @CompanyId, @SalesflowCleaningId, 'importSalesflow', '/salesflow-cleaning/import', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @ImportSalesflowId, @CompanyId, '流向导入日志', 'compose', 403,
    1, NULL, NULL, 1, '["DataCleaning:Import:Query"]'
);

-- 5. 数据查询模块
DECLARE @SalesflowId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @SalesflowId, @CompanyId, NULL, 'salesflow', '/salesflow', '/salesflow/daily/salesflow',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @SalesflowId, @CompanyId, '数据查询', 'compose', 5,
    1, NULL, NULL, 1, NULL
);

-- 日数据查询父级
DECLARE @DailyQueryId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DailyQueryId, @CompanyId, @SalesflowId, 'dailyQuery', '/salesflow/daily', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DailyQueryId, @CompanyId, '日数据查询', 'compose', 501,
    1, NULL, NULL, 1, '["SalesFlow:Daily:View"]'
);

-- 日销售流向查询
DECLARE @DailySalesFlowQueryId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DailySalesFlowQueryId, @CompanyId, @SalesflowId, 'dailySalesFlowQuery', '/salesflow/daily/salesflow', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DailySalesFlowQueryId, @CompanyId, '日销售流向查询', 'compose', 502,
    1, NULL, NULL, 1, '["SalesFlow:Daily:SalesFlow:Query"]'
);

-- 日采购查询
DECLARE @DailyPurchaseQueryId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DailyPurchaseQueryId, @CompanyId, @SalesflowId, 'dailyPurchaseQuery', '/salesflow/daily/purchase', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DailyPurchaseQueryId, @CompanyId, '日采购查询', 'compose', 503,
    1, NULL, NULL, 1, '["SalesFlow:Daily:Purchase:Query"]'
);

-- 日库存查询
DECLARE @DailyInventoryQueryId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DailyInventoryQueryId, @CompanyId, @SalesflowId, 'dailyInventoryQuery', '/salesflow/daily/inventory', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DailyInventoryQueryId, @CompanyId, '日库存查询', 'compose', 504,
    1, NULL, NULL, 1, '["SalesFlow:Daily:Inventory:Query"]'
);

-- 月数据查询父级
DECLARE @MonthlyQueryId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @MonthlyQueryId, @CompanyId, @SalesflowId, 'monthlyQuery', '/salesflow/monthly', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @MonthlyQueryId, @CompanyId, '月数据查询', 'compose', 505,
    1, NULL, NULL, 1, '["SalesFlow:Monthly:View"]'
);

-- 月销售流向查询
DECLARE @MonthlySalesFlowQueryId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @MonthlySalesFlowQueryId, @CompanyId, @SalesflowId, 'monthlySalesFlowQuery', '/salesflow/monthly/salesflow', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @MonthlySalesFlowQueryId, @CompanyId, '月销售流向', 'compose', 506,
    1, NULL, NULL, 1, '["SalesFlow:Monthly:SalesFlow:Query"]'
);

-- 月采购查询
DECLARE @MonthlyPurchaseQueryId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @MonthlyPurchaseQueryId, @CompanyId, @SalesflowId, 'monthlyPurchaseQuery', '/salesflow/monthly/purchase', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @MonthlyPurchaseQueryId, @CompanyId, '月采购查询', 'compose', 507,
    1, NULL, NULL, 1, '["SalesFlow:Monthly:Purchase:Query"]'
);

-- 6. 系统管理模块
DECLARE @SystemId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @SystemId, @CompanyId, NULL, 'system', '/system', '/system/employee/userlist',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @SystemId, @CompanyId, '系统管理', 'compose', 6,
    1, NULL, NULL, 1, NULL
);

-- 用户管理
DECLARE @UserlistId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @UserlistId, @CompanyId, @SystemId, 'userlist', '/system/employee/userlist', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @UserlistId, @CompanyId, '用户管理', 'compose', 601,
    1, NULL, NULL, 1, '["System:Employee:Query"]'
);

-- 字典管理
DECLARE @DictionarylistId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @DictionarylistId, @CompanyId, @SystemId, 'dictionarylist', '/system/dictionary/dictionarylist', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @DictionarylistId, @CompanyId, '字典管理', 'compose', 602,
    1, NULL, NULL, 1, '["System:Dictionary:Query"]'
);

-- 日志管理父级
DECLARE @LogManagementId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @LogManagementId, @CompanyId, @SystemId, 'logManagement', '/system/log', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @LogManagementId, @CompanyId, '日志管理', 'compose', 603,
    1, NULL, NULL, 1, '["System:Log:View"]'
);

-- 操作日志查询
DECLARE @OperationlogId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @OperationlogId, @CompanyId, @SystemId, 'operationlog', '/system/log/operationlog', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @OperationlogId, @CompanyId, '操作日志查询', 'compose', 604,
    1, NULL, NULL, 1, '["System:Log:Operation:Query"]'
);

-- 异常日志查询
DECLARE @ExceptionlogId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @ExceptionlogId, @CompanyId, @SystemId, 'exceptionlog', '/system/log/exceptionlog', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @ExceptionlogId, @CompanyId, '异常日志查询', 'compose', 605,
    1, NULL, NULL, 1, '["System:Log:Exception:Query"]'
);

-- 客户端日志查询
DECLARE @ClientlogId UNIQUEIDENTIFIER = NEWID();
INSERT INTO [dbo].[VueRoute] (
    [ID], [CompanyId], [ParentId], [Name], [Path], [Redirect],
    [Valid], [IsSys], [Deleted], [Creator], [CreateTime]
) VALUES (
    @ClientlogId, @CompanyId, @SystemId, 'clientlog', '/system/log/clientlog', '',
    1, 1, 0, 'SYSTEM', GETDATE()
);

INSERT INTO [dbo].[VueRouteMeta] (
    [ID], [CompanyId], [Title], [Icon], [Rank],
    [ShowLink], [AlwaysShow], [Anonymous], [IsBreadcrumbLink], [Auths]
) VALUES (
    @ClientlogId, @CompanyId, '客户端日志查询', 'compose', 606,
    1, NULL, NULL, 1, '["System:Log:Client:Query"]'
);

    -- 提交事务
    COMMIT TRANSACTION;
    PRINT 'Portal路由数据插入成功！';

END TRY
BEGIN CATCH
    -- 回滚事务
    ROLLBACK TRANSACTION;

    -- 输出错误信息
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();

    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH;
