<template>
  <!-- 终端转换 -->
  <el-dialog v-model="dialogVisible" title="终端转换" width="900px" append-to-body>
    
    <!-- 终端信息展示区域 -->
    <div class="terminal-info-section" style="margin-bottom: 20px; padding: 16px; background-color: #f5f7fa; border-radius: 8px;">
      <el-row :gutter="20">
        <!-- 停用终端信息 -->
        <el-col :span="12">
          <div class="terminal-info-card">
            <div class="terminal-info-header" style="display: flex; align-items: center; margin-bottom: 12px;">
              <el-icon style="margin-right: 8px; color: #f56c6c;">
                <Warning />
              </el-icon>
              <span style="font-weight: bold; color: #303133;">停用终端</span>
            </div>
            <div class="terminal-info-content" v-if="disabledTerminal">
              <div class="info-item" style="margin-bottom: 8px;">
                <span class="info-label" style="color: #606266; margin-right: 8px;">名称：</span>
                <span class="info-value" style="color: #303133;">{{ disabledTerminal.name }}</span>
              </div>
              <div class="info-item" style="margin-bottom: 8px;">
                <span class="info-label" style="color: #606266; margin-right: 8px;">编码：</span>
                <span class="info-value" style="color: #303133;">{{ disabledTerminal.code }}</span>
              </div>
              <div class="info-item" style="margin-bottom: 8px;">
                <span class="info-label" style="color: #606266; margin-right: 8px;">地区：</span>
                <span class="info-value" style="color: #303133;">{{ disabledTerminal.provinceName }}-{{ disabledTerminal.cityName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label" style="color: #606266; margin-right: 8px;">收货方类型：</span>
                <span class="info-value" style="color: #303133;">{{ disabledTerminal.receiverTypeLevelOneName }}-{{ disabledTerminal.receiverTypeLevelTwoName }}-{{ disabledTerminal.receiverTypeLevelThreeName }}</span>
              </div>
            </div>
            <div v-else style="color: #909399; font-style: italic;">暂无停用终端信息</div>
          </div>
        </el-col>
        
        <!-- 转换箭头 -->
        <el-col :span="2" style="display: flex; align-items: center; justify-content: center;">
          <el-icon style="font-size: 24px; color: #409eff;">
            <Right />
          </el-icon>
        </el-col>
        
        <!-- 目标终端信息 -->
        <el-col :span="10">
          <div class="terminal-info-card">
            <div class="terminal-info-header" style="display: flex; align-items: center; margin-bottom: 12px;">
              <el-icon style="margin-right: 8px; color: #67c23a;">
                <Select />
              </el-icon>
              <span style="font-weight: bold; color: #303133;">目标终端</span>
            </div>
            <div class="terminal-info-content" v-if="selectedTargetTerminal">
              <div class="info-item" style="margin-bottom: 8px;">
                <span class="info-label" style="color: #606266; margin-right: 8px;">名称：</span>
                <span class="info-value" style="color: #303133;">{{ selectedTargetTerminal.name }}</span>
              </div>
              <div class="info-item" style="margin-bottom: 8px;">
                <span class="info-label" style="color: #606266; margin-right: 8px;">编码：</span>
                <span class="info-value" style="color: #303133;">{{ selectedTargetTerminal.code }}</span>
              </div>
              <div class="info-item" style="margin-bottom: 8px;">
                <span class="info-label" style="color: #606266; margin-right: 8px;">地区：</span>
                <span class="info-value" style="color: #303133;">{{ selectedTargetTerminal.provinceName }}-{{ selectedTargetTerminal.cityName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label" style="color: #606266; margin-right: 8px;">收货方类型：</span>
                <span class="info-value" style="color: #303133;">{{ selectedTargetTerminal.receiverTypeLevelOneName }}-{{ selectedTargetTerminal.receiverTypeLevelTwoName }}-{{ selectedTargetTerminal.receiverTypeLevelThreeName }}</span>
              </div>
            </div>
            <div v-else style="color: #909399; font-style: italic;">请从下方列表中选择目标终端</div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="medicine-group-selector">
      <!-- 搜索框 -->
      <div class="search-bar" style="margin-bottom: 16px;">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-cascader v-model="selectedCity" :options="cityList" placeholder="省份/城市"
              clearable @change="handleCascaderChange" :props="{
                expandTrigger: 'hover',
                checkStrictly: true,
                emitPath: true
              }" />
          </el-col>
          <el-col :span="6">
            <el-input v-model="searchForm.name" placeholder="请输入名称" clearable>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input v-model="searchForm.code" placeholder="请输入编码" clearable>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-button icon="Search" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 表格 -->
      <el-table :data="dataList" height="350" @row-click="handleRowClick" highlight-current-row
        style="cursor: pointer;">
        <el-table-column prop="name" label="收货方名称" width="200" />
        <el-table-column prop="code" label="Code" width="150" />
        <el-table-column prop="provinceName" label="省份" width="100" />
        <el-table-column prop="cityName" label="城市" width="100" />
        <el-table-column prop="receiverTypeName" label="收货方类型">
          <template #default="{ row }">
            {{ row.receiverTypeLevelOneName }}-{{ row.receiverTypeLevelTwoName }}-{{ row.receiverTypeLevelThreeName }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ row }">
            <el-button 
              circle 
              size="small" 
              :type="selectedTargetTerminal && selectedTargetTerminal.id === row.id ? 'primary' : 'default'"
              @click.stop="handleSelectItem(row)">
              <el-icon>
                <CircleCheck />
              </el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
        <el-pagination v-model:current-page="pagination.pageIndex"
          v-model:page-size="pagination.pageSize" :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange" @current-change="handlePageChange" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :disabled="!selectedTargetTerminal" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { CircleCheck, Warning, Right, Select } from '@element-plus/icons-vue'
import { receiverApi } from '@/api/receiverApi'
import { selectorApi } from '@/api/selectorApi'

/**
 * 终端转换选择器组件
 * 用于选择终端转换的弹框组件
 */
export default {
  name: 'TerminalConvert',
  components: {
    CircleCheck,
    Warning,
    Right,
    Select
  },
  props: {
    // 对话框显示状态
    visible: {
      type: Boolean,
      default: false
    }, 
    // 停用终端信息（从父页面传入）
    disabledTerminal: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'select'],
  setup(props, { emit }) {
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 数据列表
    const dataList = ref([])
    // 级联选择器选中的省市
    const selectedCity = ref([])
    // 省市级联选择器数据
    const cityList = ref([])
    // 选中的目标终端
    const selectedTargetTerminal = ref(null)
    
    // 搜索表单数据
    const searchForm = reactive({
      provinceId: '',          // 省份ID
      cityId: '',              // 城市ID
      name: '',         // 名称搜索关键词
      code: ''          // 编码搜索关键词
    })
    
    // 分页数据
    const pagination = reactive({
      pageIndex: 1,
      pageSize: 10,
      total: 0
    })

    /**
     * 加载省市级联选项
     */
    const loadCityOptions = async () => {
      try {
        const response = await selectorApi.ProvinceCitySelect()
        if (response.data) {
          cityList.value = response.data || []
        }
      } catch (error) {
        console.error('加载省市选项失败:', error)
      }
    }

    /**
     * 加载数据列表
     */
    const loadDataList = async () => {
      try {
        const params = {
          pageIndex: pagination.pageIndex,
          pageSize: pagination.pageSize
        }

        // 添加搜索条件
        if (searchForm.provinceId) {
          params.provinceId = searchForm.provinceId
        }
        if (searchForm.cityId) {
          params.cityId = searchForm.cityId
        }
        if (searchForm.name) {
          params.name = searchForm.name
        }
        if (searchForm.code) {
          params.code = searchForm.code
        }

        // 排除指定记录
        if (props.disabledTerminal) {
          params.excludeId = props.disabledTerminal.id
        }
        
        // 只查询启用状态的记录
        params.status = [10]
        
        const response = await receiverApi.queryReceiver(params)
        if (response.data && response.data.success) {
          dataList.value = response.data.datas || []
          pagination.total = response.data.total || 0
        }
      } catch (error) {
        console.error('加载数据列表失败:', error)
      }
    }

    /**
     * 级联选择器变化处理
     */
    const handleCascaderChange = (value) => {
      if (value && value.length > 0) {
        searchForm.provinceId = value[0] || ''
        searchForm.cityId = value[1] || ''
      } else {
        searchForm.provinceId = ''
        searchForm.cityId = ''
      }
    }

    /**
     * 搜索处理
     */
    const handleSearch = () => {
      pagination.pageIndex = 1
      loadDataList()
    }

    /**
     * 分页大小改变处理
     */
    const handlePageSizeChange = (size) => {
      pagination.pageSize = size
      pagination.pageIndex = 1
      loadDataList()
    }

    /**
     * 页码改变处理
     */
    const handlePageChange = (page) => {
      pagination.pageIndex = page
      loadDataList()
    }

    /**
     * 表格行点击处理
     */
    const handleRowClick = (row) => {
      handleSelectItem(row)
    }

    /**
     * 选择项目处理
     */
    const handleSelectItem = (row) => {
      selectedTargetTerminal.value = row
      // 只选择，不立即提交
    }

    /**
     * 确定按钮处理
     */
    const handleConfirm = () => {
      if (selectedTargetTerminal.value) {
        emit('select', selectedTargetTerminal.value)
        // 不在这里关闭对话框，由父组件处理
      }
    }

    /**
     * 关闭弹框处理
     */
    const handleClose = () => {
      dialogVisible.value = false
    }

    /**
     * 重置搜索表单
     */
    const resetSearchForm = () => {
      Object.assign(searchForm, {
        provinceId: '',
        cityId: '',
        name: '',
        code: ''
      })
      selectedCity.value = []
      selectedTargetTerminal.value = null
      pagination.pageIndex = 1
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        // 重置搜索表单并加载数据
        resetSearchForm()
        loadDataList()
      }
    })

    // 组件挂载时加载选项数据
    onMounted(() => {
      loadCityOptions()
    })

    return {
      dialogVisible,
      dataList,
      selectedCity,
      cityList,
      searchForm,
      pagination,
      selectedTargetTerminal,
      handleCascaderChange,
      handleSearch,
      handlePageSizeChange,
      handlePageChange,
      handleRowClick,
      handleSelectItem,
      handleConfirm,
      handleClose
    }
  }
}
</script>

 