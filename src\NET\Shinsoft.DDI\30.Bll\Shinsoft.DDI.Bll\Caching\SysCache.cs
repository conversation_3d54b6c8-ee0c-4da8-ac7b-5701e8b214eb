﻿using Shinsoft.Core.Caching;
using Shinsoft.Core.Caching.Memory;
using Shinsoft.Core.Caching.Redis;
using Shinsoft.Core.I18n;

namespace Shinsoft.DDI.Bll.Caching
{
    public class SysCache : BaseEntityCacheClient<SysBll>, II18nTranslator
    {
        #region override

        protected override CacheType CurrentType => Config.SysRedis.IsEmpty() ? CacheType.Memory : CacheType.Redis;

        private MemoryCacheClient? _memoryCache;

        protected override MemoryCacheClient MemoryCache => _memoryCache ??= new MemoryCacheClient();

        protected override void FlushMemoryCache()
        {
            _memoryCache?.Flush();
            _memoryCache = null;
        }

        private RedisClient? _redisCache;

        protected override RedisClient RedisCache => _redisCache ??= new RedisClient(Config.SysRedis);

        protected override void FlushRedisCache()
        {
            _redisCache?.Flush();
            _redisCache = null;
        }

        protected override void DisposeRedisClinet()
        {
            _redisCache?.Dispose();
        }

        #endregion override

        #region GetData

        protected List<CultureText> GetClientCultureTexts()
        {
            var datas = new List<CultureText>();

            var cultures = this.SysCultures;

            foreach (var culture in cultures)
            {
                var data = this.GetClientCultureText(culture.Culture);

                if (data != null)
                {
                    datas.Add(data);
                }
            }

            return datas;
        }

        protected CultureText? GetClientCultureText(string culture)
        {
            CultureText? data = null;

            if (this.SysCultures.Any(c => c.Culture == culture))
            {
                data = new CultureText
                {
                    Culture = culture,
                };

                var i18ns = this.I18ns
                    .Where(p => p.EnumType == I18nType.Culture)
                    .Where(p => p.EnumFlags.HasFlag(I18nFlag.Client))
                    .Where(p => p.CultureText.ContainsKey(culture))
                    .ToList();

                var allTexts = new List<I18nText>();

                if (i18ns.Count > 0)
                {
                    foreach (var i18n in i18ns)
                    {
                        InitI18nText(i18n, allTexts, data);
                    }

                    if (data.Texts.Count > 1)
                    {
                        data.Texts = [.. data.Texts.OrderBy(p => p.Key)];

                        foreach (var text in data.Texts)
                        {
                            OrderI18nText(text);
                        }
                    }
                }
            }

            return data;
        }

        protected static I18nText InitI18nText(I18n i18n, List<I18nText> allTexts, CultureText data)
        {
            var text = allTexts.FirstOrDefault(p => p.Group == i18n.Group && p.Key == i18n.Key);

            var isNew = false;

            if (text == null)
            {
                text = new I18nText
                {
                    Group = i18n.Group,
                    Key = i18n.Key
                };

                allTexts.Add(text);
                isNew = true;
            }

            if (i18n.CultureText.TryGetValue(data.Culture, out string? value))
            {
                text.Text = value;
            }

            if (i18n.Parent == null)
            {
                if (isNew)
                {
                    data.Texts.Add(text);
                }
            }
            else
            {
                var parent = InitI18nText(i18n.Parent, allTexts, data);

                parent.Children ??= [];

                parent.Children.Add(text);
            }

            return text;
        }

        protected static void OrderI18nText(I18nText text)
        {
            if (text.Children?.Count > 0)
            {
                if (text.Children.Count > 1)
                {
                    text.Children = [.. text.Children.OrderBy(p => p.Key)];
                }

                foreach (var child in text.Children)
                {
                    OrderI18nText(child);
                }
            }
        }

        #endregion GetData

        #region GetAll

        protected List<Auth> GetAllAuth()
        {
            var all = this.Repo.GetEntities<Auth>(p => p.Valid);

            var entities = new List<Auth>();

            foreach (var entity in all.Where(p => !p.ParentId.HasValue))
            {
                InitValidAuths(entity, entities, all);
            }

            return entities;
        }

        private static void InitValidAuths(Auth auth, List<Auth> valids, List<Auth> all)
        {
            if (auth.Valid)
            {
                valids.Add(auth);

                var children = all.Where(p => p.ParentId == auth.ID).ToList();

                foreach (var child in children)
                {
                    InitValidAuths(child, valids, all);
                }
            }
        }

        protected List<SysCulture> GetAllSysCulture()
        {
            return this.Repo.GetEntities<SysCulture>($"{SysCulture.Columns.IsDefault} DESC,{SysCulture.Columns.Name} ASC");
        }

        protected List<I18n> GetAllI18n()
        {
            var all = this.Repo.GetEntities<I18n>(p => p.Valid && !p.CompanyId.HasValue);

            var entities = new List<I18n>();

            var roots = all.Where(p => !p.ParentId.HasValue).ToList();

            foreach (var entity in roots)
            {
                InitValidI18ns(entity, entities, all);
            }

            return entities;
        }

        private static void InitValidI18ns(I18n entity, List<I18n> valids, List<I18n> all)
        {
            if (entity.Valid)
            {
                valids.Add(entity);

                var children = all.Where(p => p.ParentId == entity.ID).ToList();

                foreach (var child in children)
                {
                    InitValidI18ns(child, valids, all);
                }
            }
        }

        #endregion GetAll

        #region ToClone

        protected virtual Company ToClone(Company entity)
        {
            var clone = entity.Clone();

            clone.Cfg = entity.Cfg?.Clone();

            return clone;
        }

        #endregion ToClone

        #region InitClones

        protected virtual List<Auth> InitClones(List<Auth> clones)
        {
            var roots = clones.Where(p => !p.ParentId.HasValue).ToList();

            foreach (var root in roots)
            {
                InitAuth(root, clones);
            }

            foreach (var clone in clones)
            {
                var children = clones.Where(p => p.ParentId == clone.ID && !p.EnumFlags.HasFlag(AuthFlag.Invisible)).ToList();
                var invisibleChildren = clones.Where(p => p.ParentId == clone.ID && p.EnumFlags.HasFlag(AuthFlag.Invisible)).ToList();

                clone.Children = children;
                clone.InvisibleChilren = invisibleChildren;
            }

            return clones;
        }

        private static void InitAuth(Auth clone, List<Auth> clones)
        {
            if (clone.Parent != null)
            {
                clone.EnumFlags |= clone.Parent.EnumFlags;
            }

            var children = clones.Where(p => p.ParentId == clone.ID).ToList();

            foreach (var child in children)
            {
                InitAuth(child, clones);
            }
        }

        protected virtual List<I18n> InitClones(List<I18n> clones)
        {
            var cultures = this.Repo.GetEntities<I18nCulture>(p => !p.CompanyId.HasValue);

            var roots = clones.Where(p => !p.ParentId.HasValue).ToList();

            foreach (var root in roots)
            {
                this.InitI18n(root, clones, cultures);
            }

            return clones;
        }

        private void InitI18n(I18n clone, List<I18n> clones, List<I18nCulture> cultures)
        {
            if (clone.Parent != null && clone.EnumFlags == I18nFlag.None)
            {
                clone.EnumFlags |= clone.Parent.EnumFlags;
            }

            if (clone.EnumType == I18nType.Culture)
            {
                clone.CultureText.Clear();

                if (!clone.Text.IsEmpty())
                {
                    clone.CultureText.TryAdd(this.DefCulture.Culture, clone.Text);
                }

                var currCultures = cultures
                    .Where(p => p.I18nId == clone.ID)
                    .OrderBy(p => p.CompanyId.HasValue ? 1 : 0)
                    .ToList();

                foreach (var culture in currCultures)
                {
                    if (clone.CultureText.TryGetValue(culture.Culture, out string? text))
                    {
                        clone.CultureText[culture.Culture] = culture.Text;
                    }
                    else
                    {
                        clone.CultureText.TryAdd(culture.Culture, culture.Text);
                    }
                }
            }

            var children = clones.Where(p => p.ParentId == clone.ID).ToList();

            foreach (var child in children)
            {
                InitI18n(child, clones, cultures);
            }
        }

        #endregion InitClones

        #region 登录相关缓存，存放于内存中

        #region Auth

        public List<Auth> Auths => this.GetMemoryCaches<Auth>();

        public Auth GetAuth(Guid id)
        {
            var entity = this.Auths.FirstOrDefault(p => p.ID == id) ?? throw new InvalidOperationException($"{nameof(SysCache)}:未找到ID为【{id}】的权限");

            return entity;
        }

        public Auth? GetAuth(string code)
        {
            return this.Auths.FirstOrDefault(p => p.Code == code);
        }

        public List<Auth> GetAuths(List<Guid> ids)
        {
            return this.Auths.Where(p => ids.Contains(p.ID)).ToList();
        }

        public List<Auth> GetAuthsByPerant(Guid parentId)
        {
            return this.Auths.Where(p => p.ParentId == parentId).ToList();
        }

        public List<Auth> GetAuthsByParent(string parentCode)
        {
            return this.Auths.Where(p => p.Parent?.Code == parentCode).ToList();
        }

        #endregion Auth

        #endregion 登录相关缓存，存放于内存中

        #region Company

        public List<Company> Companies => this.GetHashCaches<Company>();

        public Company GetCompany(Guid id)
        {
            var company =
                this.GetHashCache(id.ToString(), (field) => this.Repo.Get<Company>(field.As<Guid>()))
                ?? throw new InvalidOperationException($"SysCache:不存在ID为【{id}】的公司");

            return company;
        }

        public Company? GetCompany(string companyCode)
        {
            var company = this.Companies.Where(p => p.Code == companyCode).FirstOrDefault();

            return company;
        }

        public List<Company> GetValidCompanies()
        {
            return [.. this.Companies.Where(p => p.Valid).OrderBy(p => p.ID == Config.DefaultCompanyId ? 0 : 1).ThenBy(p => p.Code)];
        }

        #endregion Company

        #region SysSetting

        public List<SysSetting> SysSettings => this.GetCaches<SysSetting>();

        public SysSetting? GetSysSetting(string key)
        {
            return this.GetHashCache(key, (field) => this.Repo.GetEntity<SysSetting>(p => p.Key == field));
        }

        #endregion SysSetting

        #region SysCulture

        public List<SysCulture> SysCultures => this.GetCaches<SysCulture>();

        private SysCulture? _defCulture = null;

        public SysCulture DefCulture => _defCulture ??= this.SysCultures.First(p => p.IsDefault);

        public SysCulture? GetSysCulture(string culture)
        {
            return this.SysCultures.FirstOrDefault(p => p.Culture == culture);
        }

        #endregion SysCulture

        #region I18n

        public List<I18n> I18ns => this.GetCaches<I18n>();

        private List<I18n>? _i18nRoots = null;

        public List<I18n> I18nRoots => _i18nRoots ??= this.I18ns.Where(p => !p.ParentId.HasValue).ToList();

        public string Translate(string key, string? culture = null)
        {
            var lastDot = key.LastIndexOf('.');

            string group;

            if (lastDot < 0)
            {
                group = string.Empty;
            }
            else
            {
                group = key[..lastDot];
                key = key[(lastDot + 1)..];
            }

            return this.Translate(group, key, culture);
        }

        public string Translate(string group, string key, string? culture = null)
        {
            if (culture.IsEmpty())
            {
                culture = this.DefCulture!.Culture;
            }

            var entity = this.I18ns
                .Where(p => p.EnumType == I18nType.Culture && p.Group == group && p.Key == key)
                .FirstOrDefault();

            string? text = null;

            if (entity != null && !entity.CultureText.TryGetValue(culture!, out text))
            {
                text = entity.Text;
            }

            return text ?? (group.IsEmpty() ? key : $"{group}.{key}");
        }

        #endregion I18n

        #region ClientCultures

        private const string ClientCultureKey = "ClientCulture";

        public List<CultureText> ClientCultures => this.GetHashCaches<CultureText>(this.GetClientCultureTexts, ClientCultureKey);

        public CultureText? GetClientCulture(string culture)
        {
            return this.GetHashCache<CultureText>(culture, this.GetClientCultureText, this.GetClientCultureTexts, ClientCultureKey);
        }

        public List<I18nText> GetClientTexts(string culture)
        {
            var clientCulture = this.GetClientCulture(culture);

            return clientCulture?.Texts ?? [];
        }

        public void RemoveI18nCache()
        {
            this.RemoveCache<I18n>();
            this.RemoveKey(ClientCultureKey);
        }

        #endregion ClientCultures

        #region Redis缓存

        #region 地理信息
        public List<CascaderModel>? ProvinceCityCascader => this.RedisCache.GetSet("ProvinceCity", QueryProvinceCityCascader);
        protected List<CascaderModel> QueryProvinceCityCascader()
        {
            var entities = this.Repo.GetEntities<Province>($"+{Province.Columns.Code}");

            return entities.Select(o => new CascaderModel
            {
                Label = o.Name,
                Value = o.ID,
                Children = o.City.OrderBy(o => o.Code).Select(p => new CascaderModel
                {
                    Label = p.Name,
                    Value = p.ID
                }).ToList()
            }).ToList();

        }
        public List<CascaderModel>? ProvinceCityCountyCascader => this.RedisCache.GetSet("ProvinceCityCounty", QueryProvinceCityCountyCascader);
        protected List<CascaderModel> QueryProvinceCityCountyCascader()
        {
            var entities = this.Repo.GetEntities<Province>($"+{Province.Columns.Code}");
            return entities.Select(o => new CascaderModel
            {
                Label = o.Name,
                Value = o.ID,
                Children = o.City.OrderBy(o => o.Code).Select(p => new CascaderModel
                {
                    Label = p.Name,
                    Value = p.ID,
                    Children = p.County.OrderBy(p => p.Code).Select(c => new CascaderModel
                    {
                        Label = c.Name,
                        Value = c.ID
                    }).ToList()
                }).ToList()
            }).ToList();
        }

        #endregion

        #region 收货方类型
        public List<CascaderModel>? ReceiverTypeCascader => this.RedisCache.GetSet("ReceiverType", QueryReceiverTypeCascader);
        protected List<CascaderModel> QueryReceiverTypeCascader()
        {
            var entities = this.Repo.GetEntities<ReceiverType>(o => o.ParentReceiverTypeId == null, $"+{ReceiverType.Columns.Code}");
            return entities.Select(o => new CascaderModel
            {
                Label = o.Name,
                Value = o.ID,
                Code = o.Code,
                Children = o.SubReceiverType.OrderBy(o => o.Code).Select(p => new CascaderModel
                {
                    Label = p.Name,
                    Value = p.ID,
                    Code = o.Code,
                    Children = p.SubReceiverType.OrderBy(p => p.Code).Select(c => new CascaderModel
                    {
                        Label = c.Name,
                        Value = c.ID,
                        Code = o.Code
                    }).ToList()
                }).ToList()
            }).ToList();
        }
        #endregion

        #endregion
    }
}