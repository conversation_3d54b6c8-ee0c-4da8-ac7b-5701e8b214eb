﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Entities
{
    public partial class User : ICompanyUser, IIdentity, IUserAuths
    {
        #region IUser

        [NotMapped, XmlIgnore, JsonIgnore]
        public string UniqueName => this.LoginName;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string UserId => this.ID.AsString();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? EmployeeId => (this.Employee?.ID).AsString();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? EmployeeName => this.Employee?.DisplayName;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? AgentId => (this.Agent?.ID).AsString();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? AgentName => this.Agent?.DisplayName;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? RoleId => (this.CurrentRole?.ID).AsString();

        [NotMapped, XmlIgnore, JsonIgnore]
        public string? RoleName => this.CurrentRole?.Name;

        #endregion IUser

        #region ICompanyUser

        [NotMapped, JsonIgnore, XmlIgnore]
        string ICompanyUser.CompanyId => this.OperatorCompanyId.AsString();

        [NotMapped, JsonIgnore, XmlIgnore]
        string ICompanyUser.CompanyCode => this.OperatorCompany?.Code ?? string.Empty;

        [NotMapped, JsonIgnore, XmlIgnore]
        string ICompanyUser.CompanyName => this.OperatorCompany?.Name ?? string.Empty;

        #endregion ICompanyUser

        #region IUserAuths

        /// <summary>
        /// 权限
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public List<UserAuth>? Auths { get; set; }

        #endregion IUserAuths

        #region Operator Company

        private Company? _OperatorCompany = null;

        /// <summary>
        /// 操作公司（可能为空）
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public virtual Company? OperatorCompany
        {
            get => _OperatorCompany ??= this.Employee != null ? this.Employee.Company : this.DefaultCompany;
            set
            {
                _OperatorCompany = value;
                _OperatorCompanyId = value?.ID;
            }
        }

        private Guid? _OperatorCompanyId = null;

        /// <summary>
        /// 操作公司ID（可能为空）
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public virtual Guid? OperatorCompanyId => _OperatorCompanyId ??= this.OperatorCompany?.ID ?? this.Employee?.CompanyId ?? this.DefaultCompanyId;

        #endregion Operator Company

        #region Current Company

        /// <summary>
        /// 当前公司（操作公司为空时报错）
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public Company CurrentCompany
        {
            get => this.OperatorCompany ?? throw new InvalidOperationException("当前公司不存在");
            set => this.OperatorCompany = value;
        }

        /// <summary>
        /// 当前公司ID（操作公司为空时报错）
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public Guid CurrentCompanyId => this.CurrentCompany.ID;

        #endregion Current Company

        #region Employee

        /// <summary>
        /// 当前身份员工
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Employee? Employee { get; set; }

        [NotMapped, XmlIgnore, JsonIgnore]
        public string EmployeeEmail => this.Employee?.Email ?? this.Email;

        [NotMapped, XmlIgnore, JsonIgnore]
        public string EmployeeMobile => this.Employee?.Mobile ?? this.Mobile;

        #endregion Employee

        #region Current Shipper

        private Shipper _shipper;

        /// <summary>
        /// 当前货主
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public Shipper CurrentShipper
        {
            get => _shipper ?? throw new InvalidOperationException("当前货主不存在");
            set => this._shipper = value;
        }

        /// <summary>
        /// 当前货主ID
        /// </summary>
        [NotMapped, JsonIgnore, XmlIgnore]
        public Guid CurrentShipperId => this.CurrentShipper.ID;

        #endregion Current Shipper

        #region IIdentityKey

        [NotMapped, XmlIgnore, JsonIgnore]
        public IIdentityKey IdentityKey => new ApiIdentityKey
        {
            ShipperId = this.CurrentShipperId,
            Culture = this.Culture,
            UserId = this.UserId,
            EmployeeId = this.EmployeeId,
            AgentId = this.AgentId,
            RoleId = this.RoleId
        };

        #endregion IIdentityKey

        #region Agent

        /// <summary>
        /// 代理人员工
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Employee? Agent { get; set; }

        [NotMapped, XmlIgnore, JsonIgnore]
        public bool IsAgent => this.Agent != null;

        #endregion Agent

        #region LineManager

        /// <summary>
        /// 当前身份直属上级领导
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Employee? LineManager { get; set; }

        #endregion LineManager

        #region Role

        /// <summary>
        /// 当前身份员工
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Role? CurrentRole { get; set; }

        #endregion Role

        /// <summary>
        /// 密码是否已过期
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public virtual bool PwdExpired => this.PwdExpireTime <= SysDateTime.Now;

        /// <summary>
        /// 当前用户可用身份
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<Company, List<Employee>>? MyIdentities { get; set; }

        /// <summary>
        /// 当前用户可代理身份
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public Dictionary<Employee, List<Employee>>? MyDelegates { get; set; }

        /// <summary>
        /// 岗位
        /// </summary>
        [NotMapped, XmlIgnore, JsonIgnore]
        public List<Station>? Stations { get; set; }
    }
}