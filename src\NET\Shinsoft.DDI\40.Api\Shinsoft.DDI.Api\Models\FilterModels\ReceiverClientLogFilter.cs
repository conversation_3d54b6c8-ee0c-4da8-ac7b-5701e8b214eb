﻿namespace Shinsoft.DDI.Api.Models
{
    public partial class ReceiverClientLogFilter
    {
        /// <summary>
        /// 客户编码
        /// </summary>
        [Description("客户编码")]
        [DynamicQueryColumn(typeof(ReceiverClientLog), ReceiverClientLog.Columns.ReceiverCode, Operation = Operation.StringIntelligence)]
        public string? ReceiverCode { get; set; }


        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        [DynamicQueryColumn(typeof(ReceiverClientLog), ReceiverClientLog.Columns.ReceiverName, Operation = Operation.StringIntelligence)]
        public string? ReceiverName { get; set; }


        /// <summary>
        /// 日志内容
        /// </summary>
        [Description("日志内容")]
        [DynamicQueryColumn(typeof(ReceiverClientLog), ReceiverClientLog.Columns.Message, Operation = Operation.StringIntelligence)]
        public string? Message { get; set; }


        /// <summary>
        /// 日志时间
        /// </summary>
        [Description("日志时间")]
        [DynamicQueryColumn(typeof(ReceiverClientLog), ReceiverClientLog.Columns.LogTime, Operation = Operation.BetweenWithMinAndMax)]
        public List<DateTime>? LogTimeRange { get; set; }

    }
}
